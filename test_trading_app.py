#!/usr/bin/env python3
"""
اختبار تطبيق التداول مع الاستراتيجية الجديدة
===============================================
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🧪 Testing Trading App with Strategy V2 Integration")
print("=" * 60)

try:
    # اختبار استيراد التطبيق
    print("📦 Importing TradingApp...")
    from trading_app import TradingApp
    print("✅ TradingApp imported successfully")
    
    # إنشاء مثيل مع الاستراتيجية V2
    print("🚀 Creating TradingApp instance with Strategy V2...")
    app = TradingApp(use_strategy_v2=True)
    print("✅ TradingApp instance created successfully")
    
    # عرض معلومات الاستراتيجية
    print(f"📊 Using Strategy: {'V2 (Advanced)' if app.using_v2 else 'V1 (Legacy)'}")
    
    if app.using_v2 and app.strategy_manager:
        strategy_info = app.strategy_manager.get_strategy_info()
        print(f"🔧 Strategy Manager: {strategy_info['strategy_version']}")
        print(f"📈 V2 Available: {strategy_info['v2_available']}")
        print(f"📉 V1 Available: {strategy_info['v1_available']}")
    
    # اختبار تحويل نتيجة V2
    print("\n🔄 Testing V2 result conversion...")
    test_v2_result = {
        'signal': 'CALL',
        'confidence': 85.5,
        'expiry_minutes': 3,
        'layer_agreement': {
            'supporting_layers': ['technical', 'quantitative', 'ai'],
            'total_active_layers': 4,
            'agreement_ratio': 0.75
        }
    }
    
    converted_signal = app._convert_v2_result_to_signal(test_v2_result)
    print(f"✅ Conversion successful:")
    print(f"   Decision: {converted_signal['decision']}")
    print(f"   Direction: {converted_signal['direction']}")
    print(f"   Confidence: {converted_signal['confidence']:.1f}%")
    print(f"   Agreeing Layers: {converted_signal['layer_agreement']['agreeing_layers']}/4")
    
    print("\n✅ All tests passed successfully!")
    print("🎉 Trading app is ready to use with Strategy V2!")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
