"""
Behavioral Analysis Layer - الطبقة الثالثة
==========================================
تحليل سلوكي متقدم لنفسية السوق وسلوك المتداولين
"""

import json
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class BehavioralAnalysisLayer:
    """الطبقة الثالثة: التحليل السلوكي المتقدم"""
    
    def __init__(self):
        """تهيئة طبقة التحليل السلوكي"""
        self.name = "Behavioral Analysis Layer"
        self.weight = 0.25  # وزن هذه الطبقة في القرار النهائي
        
        # إعدادات التحليل السلوكي
        self.fear_greed_threshold = 0.7
        self.volume_spike_threshold = 1.5
        self.reversal_pattern_window = 5
        self.sentiment_window = 20
        
        # مؤشرات السلوك
        self.behavioral_indicators = {
            'fear_index': 0,
            'greed_index': 0,
            'panic_selling': False,
            'euphoria_buying': False,
            'market_sentiment': 'NEUTRAL'
        }
        
        logger.info(f"✅ {self.name} initialized")
    
    def analyze_pair(self, pair_name: str) -> Dict[str, Any]:
        """
        تحليل سلوكي شامل للزوج
        
        Args:
            pair_name: اسم زوج العملات
            
        Returns:
            نتيجة التحليل السلوكي مع الإشارة والثقة وزمن الصفقة
        """
        try:
            # قراءة البيانات التاريخية
            historical_data = self._load_historical_data(pair_name)
            if not historical_data or len(historical_data) < 20:
                return self._create_error_result("Insufficient historical data for behavioral analysis")
            
            # قراءة بيانات المؤشرات
            indicators_data = self._load_indicators_data(pair_name)
            
            # تحضير البيانات
            df = self._prepare_dataframe(historical_data)
            
            # التحليلات السلوكية
            fear_greed_analysis = self._analyze_fear_greed_index(df)
            volume_behavior_analysis = self._analyze_volume_behavior(df)
            reversal_psychology_analysis = self._analyze_reversal_psychology(df)
            sentiment_analysis = self._analyze_market_sentiment(df, indicators_data)
            crowd_behavior_analysis = self._analyze_crowd_behavior(df)
            
            # تحليل أنماط الذعر والطمع
            panic_euphoria_analysis = self._analyze_panic_euphoria_patterns(df)
            
            # دمج جميع التحليلات
            final_signal = self._combine_behavioral_analyses(
                fear_greed_analysis,
                volume_behavior_analysis,
                reversal_psychology_analysis,
                sentiment_analysis,
                crowd_behavior_analysis,
                panic_euphoria_analysis
            )
            
            return final_signal
            
        except Exception as e:
            logger.error(f"Error in behavioral analysis for {pair_name}: {e}")
            return self._create_error_result(str(e))
    
    def _load_historical_data(self, pair_name: str) -> List[Dict[str, Any]]:
        """قراءة البيانات التاريخية"""
        try:
            file_path = f"data/historical/{pair_name}.json"
            if not os.path.exists(file_path):
                return []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            candles = data.get('candles', [])
            live_candle = data.get('live_candle')
            
            # إضافة الشمعة الحية إذا كانت متاحة
            if live_candle:
                candles.append(live_candle)
            
            # أخذ آخر 100 شمعة مع التأكد من وجود بيانات كافية
            recent_candles = candles[-100:] if len(candles) > 100 else candles
            logger.info(f"Loaded {len(recent_candles)} candles for behavioral analysis of {pair_name}")
            return recent_candles
            
        except Exception as e:
            logger.error(f"Error loading historical data for {pair_name}: {e}")
            return []
    
    def _load_indicators_data(self, pair_name: str) -> Dict[str, Any]:
        """قراءة بيانات المؤشرات"""
        try:
            file_path = f"data/indicators/{pair_name}_indicators.json"
            if not os.path.exists(file_path):
                return {}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return data.get('indicators', {})
            
        except Exception as e:
            logger.error(f"Error loading indicators data for {pair_name}: {e}")
            return {}
    
    def _prepare_dataframe(self, candles: List[Dict[str, Any]]) -> pd.DataFrame:
        """تحضير DataFrame للتحليل السلوكي"""
        try:
            df = pd.DataFrame(candles)
            
            # تحويل الأعمدة إلى أرقام
            numeric_columns = ['open', 'high', 'low', 'close', 'time']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # إضافة عمود الحجم إذا كان متاحاً
            if 'ticks' in df.columns:
                df['volume'] = pd.to_numeric(df['ticks'], errors='coerce')
            else:
                # محاكاة الحجم بناءً على المدى السعري
                df['volume'] = (df['high'] - df['low']) * 1000
            
            # حساب المؤشرات السلوكية
            df['price_change'] = df['close'] - df['open']
            df['price_change_pct'] = df['price_change'] / df['open'] * 100
            df['body_size'] = abs(df['close'] - df['open'])
            df['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
            df['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
            df['total_range'] = df['high'] - df['low']
            
            # مؤشرات الخوف والطمع
            df['fear_indicator'] = self._calculate_fear_indicator(df)
            df['greed_indicator'] = self._calculate_greed_indicator(df)
            
            # مؤشرات السلوك الجماعي
            df['crowd_sentiment'] = self._calculate_crowd_sentiment(df)
            
            return df.dropna()
            
        except Exception as e:
            logger.error(f"Error preparing behavioral dataframe: {e}")
            return pd.DataFrame()
    
    def _calculate_fear_indicator(self, df: pd.DataFrame) -> pd.Series:
        """حساب مؤشر الخوف"""
        try:
            # الخوف يظهر في:
            # 1. شموع هابطة بظلال سفلية طويلة
            # 2. انخفاضات حادة مع حجم عالي
            # 3. تقلبات عالية مع اتجاه هابط
            
            fear_signals = pd.Series(0.0, index=df.index)
            
            # إشارة الخوف من الظلال الطويلة
            long_lower_shadow = df['lower_shadow'] > df['body_size'] * 2
            bearish_candles = df['close'] < df['open']
            fear_signals += (long_lower_shadow & bearish_candles).astype(float) * 0.3
            
            # إشارة الخوف من الانخفاضات الحادة
            sharp_decline = df['price_change_pct'] < -1.0
            fear_signals += sharp_decline.astype(float) * 0.4
            
            # إشارة الخوف من التقلبات العالية
            high_volatility = df['total_range'] > df['total_range'].rolling(10).mean() * 1.5
            fear_signals += (high_volatility & bearish_candles).astype(float) * 0.3
            
            return fear_signals.rolling(window=5).mean()
            
        except Exception:
            return pd.Series(0.0, index=df.index)
    
    def _calculate_greed_indicator(self, df: pd.DataFrame) -> pd.Series:
        """حساب مؤشر الطمع"""
        try:
            # الطمع يظهر في:
            # 1. شموع صاعدة بظلال علوية طويلة
            # 2. ارتفاعات حادة مع حجم عالي
            # 3. شموع صاعدة متتالية
            
            greed_signals = pd.Series(0.0, index=df.index)
            
            # إشارة الطمع من الظلال العلوية الطويلة
            long_upper_shadow = df['upper_shadow'] > df['body_size'] * 2
            bullish_candles = df['close'] > df['open']
            greed_signals += (long_upper_shadow & bullish_candles).astype(float) * 0.3
            
            # إشارة الطمع من الارتفاعات الحادة
            sharp_rise = df['price_change_pct'] > 1.0
            greed_signals += sharp_rise.astype(float) * 0.4
            
            # إشارة الطمع من الشموع الصاعدة المتتالية
            consecutive_bullish = bullish_candles.rolling(window=3).sum() >= 2
            greed_signals += consecutive_bullish.astype(float) * 0.3
            
            return greed_signals.rolling(window=5).mean()
            
        except Exception:
            return pd.Series(0.0, index=df.index)
    
    def _calculate_crowd_sentiment(self, df: pd.DataFrame) -> pd.Series:
        """حساب معنويات الجمهور"""
        try:
            # معنويات الجمهور تعتمد على:
            # 1. اتجاه الأسعار العام
            # 2. قوة الحركة
            # 3. استمرارية الاتجاه
            
            sentiment = pd.Series(0.0, index=df.index)
            
            # اتجاه الأسعار
            price_trend = df['close'].rolling(window=10).apply(
                lambda x: 1 if x.iloc[-1] > x.iloc[0] else -1
            )
            sentiment += price_trend * 0.4
            
            # قوة الحركة
            movement_strength = abs(df['price_change_pct']).rolling(window=5).mean()
            normalized_strength = (movement_strength - movement_strength.mean()) / movement_strength.std()
            sentiment += normalized_strength * 0.3
            
            # استمرارية الاتجاه
            trend_consistency = df['price_change'].rolling(window=5).apply(
                lambda x: len([i for i in x if i > 0]) / len(x) if len(x) > 0 else 0.5
            )
            sentiment += (trend_consistency - 0.5) * 0.3
            
            return sentiment.rolling(window=3).mean()
            
        except Exception:
            return pd.Series(0.0, index=df.index)
    
    def _analyze_fear_greed_index(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل مؤشر الخوف والطمع"""
        try:
            if len(df) < 10:
                return {"signal": "NEUTRAL", "confidence": 0}
            
            current_fear = df['fear_indicator'].iloc[-1]
            current_greed = df['greed_indicator'].iloc[-1]
            
            # حساب المتوسطات
            avg_fear = df['fear_indicator'].tail(10).mean()
            avg_greed = df['greed_indicator'].tail(10).mean()
            
            signal = "NEUTRAL"
            confidence = 0
            expiry_minutes = 2
            
            # إشارات الخوف الشديد (فرصة شراء)
            if current_fear > self.fear_greed_threshold and current_fear > avg_fear * 1.5:
                signal = "CALL"
                confidence = min(current_fear * 80, 85)
                expiry_minutes = 3  # الخوف يحتاج وقت للتعافي
            
            # إشارات الطمع الشديد (فرصة بيع)
            elif current_greed > self.fear_greed_threshold and current_greed > avg_greed * 1.5:
                signal = "PUT"
                confidence = min(current_greed * 80, 85)
                expiry_minutes = 2  # الطمع ينهار بسرعة
            
            # إشارات متوسطة
            elif current_fear > 0.5:
                signal = "CALL"
                confidence = current_fear * 50
                expiry_minutes = 2
            elif current_greed > 0.5:
                signal = "PUT"
                confidence = current_greed * 50
                expiry_minutes = 2
            
            return {
                "signal": signal,
                "confidence": confidence,
                "fear_level": current_fear,
                "greed_level": current_greed,
                "expiry_minutes": expiry_minutes,
                "reason": f"Fear-Greed analysis: Fear={current_fear:.2f}, Greed={current_greed:.2f}"
            }
            
        except Exception as e:
            logger.error(f"Error in fear-greed analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _analyze_volume_behavior(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل سلوك الحجم"""
        try:
            if len(df) < 10:
                return {"signal": "NEUTRAL", "confidence": 0}

            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume'].rolling(window=20).mean().iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1

            # تحليل ارتفاع الحجم مع الاتجاه
            current_price_change = df['price_change'].iloc[-1]

            signal = "NEUTRAL"
            confidence = 0
            expiry_minutes = 2

            # حجم عالي مع ارتفاع = قوة صاعدة
            if volume_ratio > self.volume_spike_threshold and current_price_change > 0:
                signal = "CALL"
                confidence = min(volume_ratio * 40, 80)
                expiry_minutes = 1  # الحجم العالي يعطي إشارات سريعة

            # حجم عالي مع انخفاض = قوة هابطة
            elif volume_ratio > self.volume_spike_threshold and current_price_change < 0:
                signal = "PUT"
                confidence = min(volume_ratio * 40, 80)
                expiry_minutes = 1

            # حجم منخفض = ضعف في الحركة
            elif volume_ratio < 0.7:
                # البحث عن إشارات الانعكاس
                recent_trend = df['price_change'].tail(5).sum()
                if recent_trend > 0:
                    signal = "PUT"  # انعكاس من الصعود
                    confidence = 45
                    expiry_minutes = 3
                elif recent_trend < 0:
                    signal = "CALL"  # انعكاس من الهبوط
                    confidence = 45
                    expiry_minutes = 3

            return {
                "signal": signal,
                "confidence": confidence,
                "volume_ratio": volume_ratio,
                "expiry_minutes": expiry_minutes,
                "reason": f"Volume behavior: ratio={volume_ratio:.2f}, price_change={current_price_change:.4f}"
            }

        except Exception as e:
            logger.error(f"Error in volume behavior analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _analyze_reversal_psychology(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل نفسية الانعكاس"""
        try:
            if len(df) < 10:
                return {"signal": "NEUTRAL", "confidence": 0}

            # البحث عن أنماط الانعكاس النفسية
            reversal_signals = []

            # نمط الإرهاق (Exhaustion)
            exhaustion_signal = self._detect_exhaustion_pattern(df)
            if exhaustion_signal['confidence'] > 0:
                reversal_signals.append(exhaustion_signal)

            # نمط الذعر (Panic)
            panic_signal = self._detect_panic_pattern(df)
            if panic_signal['confidence'] > 0:
                reversal_signals.append(panic_signal)

            # نمط الطمع المفرط (Euphoria)
            euphoria_signal = self._detect_euphoria_pattern(df)
            if euphoria_signal['confidence'] > 0:
                reversal_signals.append(euphoria_signal)

            # نمط التردد (Indecision)
            indecision_signal = self._detect_indecision_pattern(df)
            if indecision_signal['confidence'] > 0:
                reversal_signals.append(indecision_signal)

            if not reversal_signals:
                return {"signal": "NEUTRAL", "confidence": 0}

            # اختيار أقوى إشارة
            best_signal = max(reversal_signals, key=lambda x: x['confidence'])

            return {
                "signal": best_signal['signal'],
                "confidence": best_signal['confidence'],
                "pattern": best_signal['pattern'],
                "expiry_minutes": best_signal.get('expiry_minutes', 2),
                "reason": f"Reversal psychology: {best_signal['pattern']}"
            }

        except Exception as e:
            logger.error(f"Error in reversal psychology analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _analyze_market_sentiment(self, df: pd.DataFrame, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل معنويات السوق"""
        try:
            if len(df) < 15:
                return {"signal": "NEUTRAL", "confidence": 0}

            current_sentiment = df['crowd_sentiment'].iloc[-1]
            avg_sentiment = df['crowd_sentiment'].tail(10).mean()

            # تحليل RSI للمعنويات
            rsi = indicators.get('RSI14', 50)
            if isinstance(rsi, dict):
                rsi = rsi.get('value', 50) if 'value' in rsi else 50
            rsi = float(rsi) if rsi is not None else 50

            # تحليل MACD للزخم
            macd_data = indicators.get('MACD', {})
            macd_histogram = macd_data.get('HISTOGRAM', 0) if isinstance(macd_data, dict) else 0

            signal = "NEUTRAL"
            confidence = 0
            expiry_minutes = 2

            # معنويات إيجابية مفرطة (فرصة بيع)
            if current_sentiment > 0.7 and rsi > 70:
                signal = "PUT"
                confidence = min((current_sentiment + (rsi - 50) / 50) * 40, 85)
                expiry_minutes = 2

            # معنويات سلبية مفرطة (فرصة شراء)
            elif current_sentiment < -0.7 and rsi < 30:
                signal = "CALL"
                confidence = min((abs(current_sentiment) + (50 - rsi) / 50) * 40, 85)
                expiry_minutes = 3

            # معنويات متوسطة مع زخم
            elif abs(macd_histogram) > 0.0001:
                if macd_histogram > 0 and current_sentiment > 0:
                    signal = "CALL"
                    confidence = min(current_sentiment * 60, 70)
                elif macd_histogram < 0 and current_sentiment < 0:
                    signal = "PUT"
                    confidence = min(abs(current_sentiment) * 60, 70)

            return {
                "signal": signal,
                "confidence": confidence,
                "sentiment_level": current_sentiment,
                "rsi_level": rsi,
                "macd_momentum": macd_histogram,
                "expiry_minutes": expiry_minutes,
                "reason": f"Market sentiment: {current_sentiment:.2f}, RSI: {float(rsi):.1f}"
            }

        except Exception as e:
            logger.error(f"Error in market sentiment analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _analyze_crowd_behavior(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل سلوك الجمهور"""
        try:
            if len(df) < 20:
                return {"signal": "NEUTRAL", "confidence": 0}

            # تحليل سلوك القطيع
            herd_behavior = self._detect_herd_behavior(df)

            # تحليل مقاومة الجمهور للاتجاه
            crowd_resistance = self._detect_crowd_resistance(df)

            # تحليل نقاط التحول الجماعية
            turning_points = self._detect_crowd_turning_points(df)

            signal = "NEUTRAL"
            confidence = 0
            expiry_minutes = 2

            # سلوك القطيع قوي = انعكاس محتمل
            if herd_behavior['strength'] > 0.7:
                signal = "PUT" if herd_behavior['direction'] == "BULLISH" else "CALL"
                confidence = min(herd_behavior['strength'] * 70, 80)
                expiry_minutes = 1  # سلوك القطيع ينعكس بسرعة

            # مقاومة الجمهور = استمرار الاتجاه
            elif crowd_resistance['strength'] > 0.6:
                signal = crowd_resistance['direction']
                confidence = min(crowd_resistance['strength'] * 60, 75)
                expiry_minutes = 3

            # نقطة تحول جماعية
            elif turning_points['detected']:
                signal = turning_points['direction']
                confidence = turning_points['confidence']
                expiry_minutes = 2

            return {
                "signal": signal,
                "confidence": confidence,
                "herd_behavior": herd_behavior,
                "crowd_resistance": crowd_resistance,
                "turning_point": turning_points,
                "expiry_minutes": expiry_minutes,
                "reason": f"Crowd behavior analysis"
            }

        except Exception as e:
            logger.error(f"Error in crowd behavior analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _analyze_panic_euphoria_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل أنماط الذعر والطمع"""
        try:
            if len(df) < 10:
                return {"signal": "NEUTRAL", "confidence": 0}

            # كشف حالات الذعر
            panic_detected = self._is_panic_selling(df)

            # كشف حالات الطمع المفرط
            euphoria_detected = self._is_euphoria_buying(df)

            signal = "NEUTRAL"
            confidence = 0
            expiry_minutes = 2

            if panic_detected['detected']:
                signal = "CALL"  # الذعر = فرصة شراء
                confidence = panic_detected['intensity'] * 80
                expiry_minutes = 1  # الذعر ينتهي بسرعة

            elif euphoria_detected['detected']:
                signal = "PUT"  # الطمع المفرط = فرصة بيع
                confidence = euphoria_detected['intensity'] * 75
                expiry_minutes = 1  # الطمع ينهار بسرعة

            return {
                "signal": signal,
                "confidence": min(confidence, 90),
                "panic_detected": panic_detected,
                "euphoria_detected": euphoria_detected,
                "expiry_minutes": expiry_minutes,
                "reason": f"Panic-Euphoria analysis"
            }

        except Exception as e:
            logger.error(f"Error in panic-euphoria analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _combine_behavioral_analyses(self, *analyses) -> Dict[str, Any]:
        """دمج جميع التحليلات السلوكية"""
        try:
            valid_analyses = [a for a in analyses if a.get('confidence', 0) > 0]

            if not valid_analyses:
                return self._create_neutral_result("No valid behavioral signals found")

            # تجميع الإشارات
            call_signals = [a for a in valid_analyses if a.get('signal') == 'CALL']
            put_signals = [a for a in valid_analyses if a.get('signal') == 'PUT']

            if len(call_signals) > len(put_signals):
                direction = "CALL"
                supporting_analyses = call_signals
            elif len(put_signals) > len(call_signals):
                direction = "PUT"
                supporting_analyses = put_signals
            else:
                return self._create_neutral_result("Conflicting behavioral signals")

            # حساب الثقة المرجحة
            total_confidence = sum(a.get('confidence', 0) for a in supporting_analyses)
            avg_confidence = total_confidence / len(supporting_analyses)

            # تحديد زمن الصفقة
            expiry_times = [a.get('expiry_minutes', 2) for a in supporting_analyses]
            optimal_expiry = int(np.median(expiry_times)) if expiry_times else 2

            # تطبيق مرشحات الجودة - تم تخفيض العتبة أكثر
            if avg_confidence < 35:
                return self._create_neutral_result("Low behavioral confidence")

            # تعزيز الثقة للإشارات السلوكية القوية
            if len(supporting_analyses) >= 3:
                avg_confidence *= 1.1

            return {
                "layer": self.name,
                "signal": direction,
                "confidence": min(avg_confidence, 95),
                "supporting_analyses": len(supporting_analyses),
                "total_analyses": len(valid_analyses),
                "expiry_minutes": optimal_expiry,
                "details": {
                    "fear_greed": analyses[0] if len(analyses) > 0 else {},
                    "volume_behavior": analyses[1] if len(analyses) > 1 else {},
                    "reversal_psychology": analyses[2] if len(analyses) > 2 else {},
                    "market_sentiment": analyses[3] if len(analyses) > 3 else {},
                    "crowd_behavior": analyses[4] if len(analyses) > 4 else {},
                    "panic_euphoria": analyses[5] if len(analyses) > 5 else {}
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error combining behavioral analyses: {e}")
            return self._create_error_result(str(e))

    # ==================== دوال مساعدة ====================

    def _detect_exhaustion_pattern(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط الإرهاق"""
        try:
            if len(df) < 5:
                return {"signal": "NEUTRAL", "confidence": 0}

            recent_data = df.tail(5)

            # البحث عن شموع متتالية في نفس الاتجاه مع تناقص الحجم
            consecutive_bullish = (recent_data['close'] > recent_data['open']).sum()
            consecutive_bearish = (recent_data['close'] < recent_data['open']).sum()

            volume_trend = recent_data['volume'].diff().mean()

            if consecutive_bullish >= 3 and volume_trend < 0:
                return {
                    "signal": "PUT",
                    "confidence": min(consecutive_bullish * 15, 75),
                    "pattern": "BULLISH_EXHAUSTION",
                    "expiry_minutes": 2
                }
            elif consecutive_bearish >= 3 and volume_trend < 0:
                return {
                    "signal": "CALL",
                    "confidence": min(consecutive_bearish * 15, 75),
                    "pattern": "BEARISH_EXHAUSTION",
                    "expiry_minutes": 2
                }

            return {"signal": "NEUTRAL", "confidence": 0}

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0}

    def _detect_panic_pattern(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط الذعر"""
        try:
            if len(df) < 3:
                return {"signal": "NEUTRAL", "confidence": 0}

            last_candle = df.iloc[-1]

            # الذعر: انخفاض حاد مع ظل سفلي طويل وحجم عالي
            sharp_decline = last_candle['price_change_pct'] < -1.5
            long_lower_shadow = last_candle['lower_shadow'] > last_candle['body_size'] * 1.5
            high_volume = last_candle['volume'] > df['volume'].tail(10).mean() * 1.3

            if sharp_decline and long_lower_shadow and high_volume:
                return {
                    "signal": "CALL",
                    "confidence": 80,
                    "pattern": "PANIC_SELLING",
                    "expiry_minutes": 1
                }

            return {"signal": "NEUTRAL", "confidence": 0}

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0}

    def _detect_euphoria_pattern(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط الطمع المفرط"""
        try:
            if len(df) < 3:
                return {"signal": "NEUTRAL", "confidence": 0}

            last_candle = df.iloc[-1]

            # الطمع المفرط: ارتفاع حاد مع ظل علوي طويل وحجم عالي
            sharp_rise = last_candle['price_change_pct'] > 1.5
            long_upper_shadow = last_candle['upper_shadow'] > last_candle['body_size'] * 1.5
            high_volume = last_candle['volume'] > df['volume'].tail(10).mean() * 1.3

            if sharp_rise and long_upper_shadow and high_volume:
                return {
                    "signal": "PUT",
                    "confidence": 75,
                    "pattern": "EUPHORIA_BUYING",
                    "expiry_minutes": 1
                }

            return {"signal": "NEUTRAL", "confidence": 0}

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0}

    def _detect_indecision_pattern(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط التردد"""
        try:
            if len(df) < 3:
                return {"signal": "NEUTRAL", "confidence": 0}

            recent_data = df.tail(3)

            # التردد: شموع صغيرة مع ظلال طويلة
            small_bodies = (recent_data['body_size'] < recent_data['total_range'] * 0.3).sum()
            long_shadows = ((recent_data['upper_shadow'] + recent_data['lower_shadow']) >
                           recent_data['body_size'] * 2).sum()

            if small_bodies >= 2 and long_shadows >= 2:
                # تحديد الاتجاه بناءً على الاتجاه السابق
                prev_trend = df['price_change'].tail(10).sum()

                if prev_trend > 0:
                    return {
                        "signal": "PUT",
                        "confidence": 60,
                        "pattern": "INDECISION_AFTER_RISE",
                        "expiry_minutes": 3
                    }
                else:
                    return {
                        "signal": "CALL",
                        "confidence": 60,
                        "pattern": "INDECISION_AFTER_FALL",
                        "expiry_minutes": 3
                    }

            return {"signal": "NEUTRAL", "confidence": 0}

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0}

    def _detect_herd_behavior(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف سلوك القطيع"""
        try:
            if len(df) < 10:
                return {"strength": 0, "direction": "NEUTRAL"}

            recent_data = df.tail(10)

            # سلوك القطيع: حركة قوية في اتجاه واحد مع حجم متزايد
            price_changes = recent_data['price_change']
            volume_changes = recent_data['volume'].diff()

            # حساب قوة الاتجاه
            positive_changes = (price_changes > 0).sum()
            negative_changes = (price_changes < 0).sum()

            direction_strength = abs(positive_changes - negative_changes) / len(price_changes)

            # حساب قوة الحجم
            volume_strength = (volume_changes > 0).sum() / len(volume_changes.dropna())

            # قوة سلوك القطيع
            herd_strength = (direction_strength + volume_strength) / 2

            direction = "BULLISH" if positive_changes > negative_changes else "BEARISH"

            return {
                "strength": herd_strength,
                "direction": direction,
                "price_consistency": direction_strength,
                "volume_consistency": volume_strength
            }

        except Exception:
            return {"strength": 0, "direction": "NEUTRAL"}

    def _detect_crowd_resistance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف مقاومة الجمهور"""
        try:
            if len(df) < 15:
                return {"strength": 0, "direction": "NEUTRAL"}

            # مقاومة الجمهور: محاولات متكررة لكسر مستوى معين
            recent_highs = df['high'].tail(15)
            recent_lows = df['low'].tail(15)

            # البحث عن مستويات المقاومة والدعم
            resistance_level = recent_highs.quantile(0.9)
            support_level = recent_lows.quantile(0.1)

            current_price = df['close'].iloc[-1]

            # حساب عدد المحاولات
            resistance_touches = (recent_highs >= resistance_level * 0.999).sum()
            support_touches = (recent_lows <= support_level * 1.001).sum()

            if resistance_touches >= 3 and current_price >= resistance_level * 0.995:
                return {
                    "strength": min(resistance_touches / 5, 1),
                    "direction": "PUT",
                    "level": resistance_level,
                    "touches": resistance_touches
                }
            elif support_touches >= 3 and current_price <= support_level * 1.005:
                return {
                    "strength": min(support_touches / 5, 1),
                    "direction": "CALL",
                    "level": support_level,
                    "touches": support_touches
                }

            return {"strength": 0, "direction": "NEUTRAL"}

        except Exception:
            return {"strength": 0, "direction": "NEUTRAL"}

    def _detect_crowd_turning_points(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نقاط التحول الجماعية"""
        try:
            if len(df) < 10:
                return {"detected": False, "direction": "NEUTRAL", "confidence": 0}

            # نقاط التحول: تغيير مفاجئ في السلوك الجماعي
            sentiment_changes = df['crowd_sentiment'].diff().tail(5)
            volume_changes = df['volume'].pct_change().tail(5)

            # البحث عن تغييرات حادة
            sharp_sentiment_change = abs(sentiment_changes).max() > 0.5
            volume_spike = volume_changes.max() > 0.5

            if sharp_sentiment_change and volume_spike:
                latest_sentiment = df['crowd_sentiment'].iloc[-1]

                if latest_sentiment > 0.3:
                    return {
                        "detected": True,
                        "direction": "CALL",
                        "confidence": min(abs(latest_sentiment) * 80, 75)
                    }
                elif latest_sentiment < -0.3:
                    return {
                        "detected": True,
                        "direction": "PUT",
                        "confidence": min(abs(latest_sentiment) * 80, 75)
                    }

            return {"detected": False, "direction": "NEUTRAL", "confidence": 0}

        except Exception:
            return {"detected": False, "direction": "NEUTRAL", "confidence": 0}

    def _is_panic_selling(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف حالة الذعر في البيع"""
        try:
            if len(df) < 5:
                return {"detected": False, "intensity": 0}

            recent_data = df.tail(5)

            # مؤشرات الذعر
            sharp_declines = (recent_data['price_change_pct'] < -1.0).sum()
            high_volume_declines = ((recent_data['price_change'] < 0) &
                                  (recent_data['volume'] > recent_data['volume'].mean() * 1.2)).sum()
            long_lower_shadows = (recent_data['lower_shadow'] > recent_data['body_size']).sum()

            panic_score = (sharp_declines + high_volume_declines + long_lower_shadows) / 15

            return {
                "detected": panic_score > 0.4,
                "intensity": min(panic_score, 1),
                "indicators": {
                    "sharp_declines": sharp_declines,
                    "high_volume_declines": high_volume_declines,
                    "long_lower_shadows": long_lower_shadows
                }
            }

        except Exception:
            return {"detected": False, "intensity": 0}

    def _is_euphoria_buying(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف حالة الطمع المفرط في الشراء"""
        try:
            if len(df) < 5:
                return {"detected": False, "intensity": 0}

            recent_data = df.tail(5)

            # مؤشرات الطمع المفرط
            sharp_rises = (recent_data['price_change_pct'] > 1.0).sum()
            high_volume_rises = ((recent_data['price_change'] > 0) &
                               (recent_data['volume'] > recent_data['volume'].mean() * 1.2)).sum()
            long_upper_shadows = (recent_data['upper_shadow'] > recent_data['body_size']).sum()

            euphoria_score = (sharp_rises + high_volume_rises + long_upper_shadows) / 15

            return {
                "detected": euphoria_score > 0.4,
                "intensity": min(euphoria_score, 1),
                "indicators": {
                    "sharp_rises": sharp_rises,
                    "high_volume_rises": high_volume_rises,
                    "long_upper_shadows": long_upper_shadows
                }
            }

        except Exception:
            return {"detected": False, "intensity": 0}

    def _create_error_result(self, error_msg: str) -> Dict[str, Any]:
        """إنشاء نتيجة خطأ"""
        return {
            "layer": self.name,
            "signal": "NEUTRAL",
            "confidence": 0,
            "error": error_msg,
            "timestamp": datetime.now().isoformat()
        }

    def _create_neutral_result(self, reason: str) -> Dict[str, Any]:
        """إنشاء نتيجة محايدة"""
        return {
            "layer": self.name,
            "signal": "NEUTRAL",
            "confidence": 0,
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        }
