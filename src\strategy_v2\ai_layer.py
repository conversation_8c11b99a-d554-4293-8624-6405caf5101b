"""
AI Analysis Layer - الطبقة الرابعة
==================================
تحليل متقدم باستخدام الذكاء الاصطناعي والتعلم الآلي
"""

import json
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
import pickle
import warnings
warnings.filterwarnings('ignore')

# محاولة استيراد مكتبات التعلم الآلي
try:
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, classification_report
    SKLEARN_AVAILABLE = True
    print("✅ 'sklearn' library loaded successfully")
except ImportError:
    SKLEARN_AVAILABLE = False
    RandomForestClassifier = None
    GradientBoostingClassifier = None
    StandardScaler = None
    LabelEncoder = None
    train_test_split = None
    accuracy_score = None
    classification_report = None
    print("⚠️ Warning: 'sklearn' library not found. AI features will be limited.")

logger = logging.getLogger(__name__)

class AIAnalysisLayer:
    """الطبقة الرابعة: التحليل بالذكاء الاصطناعي"""
    
    def __init__(self):
        """تهيئة طبقة الذكاء الاصطناعي"""
        self.name = "AI Analysis Layer"
        self.weight = 0.25  # وزن هذه الطبقة في القرار النهائي
        
        # نماذج التعلم الآلي
        self.models = {
            'direction_predictor': None,
            'confidence_estimator': None,
            'expiry_optimizer': None
        }
        
        # معالجات البيانات
        self.scalers = {
            'features': StandardScaler(),
            'targets': LabelEncoder()
        }
        
        # إعدادات النماذج
        self.model_config = {
            'min_samples_for_training': 100,
            'feature_window': 20,
            'prediction_horizon': 5,
            'confidence_threshold': 0.8
        }
        
        # ذاكرة التدريب
        self.training_data = []
        self.model_performance = {
            'accuracy': 0.5,
            'last_training': None,
            'samples_count': 0
        }

        # إعدادات التدريب التلقائي
        self.auto_training_config = {
            'retrain_interval_hours': 6,  # إعادة التدريب كل 6 ساعات
            'min_new_samples': 10,        # الحد الأدنى للعينات الجديدة
            'performance_threshold': 0.4   # إعادة التدريب إذا انخفض الأداء
        }
        
        # تحميل النماذج المحفوظة
        self._load_saved_models()
        
        logger.info(f"✅ {self.name} initialized")
    
    def analyze_pair(self, pair_name: str) -> Dict[str, Any]:
        """
        تحليل متقدم باستخدام الذكاء الاصطناعي
        
        Args:
            pair_name: اسم زوج العملات
            
        Returns:
            نتيجة التحليل بالذكاء الاصطناعي مع الإشارة والثقة وزمن الصفقة
        """
        try:
            # قراءة البيانات التاريخية
            historical_data = self._load_historical_data(pair_name)
            if not historical_data or len(historical_data) < 50:
                return self._create_error_result("Insufficient historical data for AI analysis")
            
            # قراءة بيانات المؤشرات
            indicators_data = self._load_indicators_data(pair_name)
            if not indicators_data:
                return self._create_error_result("No indicators data available")
            
            # تحضير البيانات للتحليل
            features_df = self._prepare_features(historical_data, indicators_data)
            if features_df.empty:
                return self._create_error_result("Failed to prepare features")
            
            # التدريب التلقائي إذا لزم الأمر
            self._auto_train_models(features_df, pair_name)

            # فحص الحاجة لإعادة التدريب الدوري
            self._check_periodic_retraining(features_df, pair_name)
            
            # التنبؤ باستخدام النماذج
            direction_prediction = self._predict_direction(features_df)
            confidence_estimation = self._estimate_confidence(features_df, direction_prediction)
            expiry_optimization = self._optimize_expiry_time(features_df, direction_prediction)
            
            # تحليل الأنماط المعقدة
            pattern_analysis = self._analyze_complex_patterns(features_df)
            
            # تحليل الزخم المتقدم
            momentum_analysis = self._analyze_advanced_momentum(features_df)
            
            # دمج جميع التحليلات
            final_signal = self._combine_ai_analyses(
                direction_prediction,
                confidence_estimation,
                expiry_optimization,
                pattern_analysis,
                momentum_analysis
            )
            
            return final_signal
            
        except Exception as e:
            logger.error(f"Error in AI analysis for {pair_name}: {e}")
            return self._create_error_result(str(e))
    
    def _load_historical_data(self, pair_name: str) -> List[Dict[str, Any]]:
        """قراءة البيانات التاريخية"""
        try:
            file_path = f"data/historical/{pair_name}.json"
            if not os.path.exists(file_path):
                return []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            candles = data.get('candles', [])
            live_candle = data.get('live_candle')
            
            # إضافة الشمعة الحية إذا كانت متاحة
            if live_candle:
                candles.append(live_candle)
            
            return candles[-200:]  # آخر 200 شمعة للتدريب والتحليل
            
        except Exception as e:
            logger.error(f"Error loading historical data for {pair_name}: {e}")
            return []
    
    def _load_indicators_data(self, pair_name: str) -> Dict[str, Any]:
        """قراءة بيانات المؤشرات"""
        try:
            file_path = f"data/indicators/{pair_name}_indicators.json"
            if not os.path.exists(file_path):
                return {}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return data.get('indicators', {})
            
        except Exception as e:
            logger.error(f"Error loading indicators data for {pair_name}: {e}")
            return {}
    
    def _prepare_features(self, candles: List[Dict[str, Any]], indicators: Dict[str, Any]) -> pd.DataFrame:
        """تحضير المعالم للتعلم الآلي"""
        try:
            df = pd.DataFrame(candles)
            
            # تحويل الأعمدة إلى أرقام
            numeric_columns = ['open', 'high', 'low', 'close', 'time']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # المعالم الأساسية
            df['returns'] = df['close'].pct_change()
            df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
            df['volatility'] = df['returns'].rolling(window=10).std()
            df['price_change'] = df['close'] - df['open']
            df['price_range'] = df['high'] - df['low']
            df['body_size'] = abs(df['close'] - df['open'])
            df['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
            df['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
            
            # معالم المتوسطات المتحركة
            for period in [5, 10, 20]:
                df[f'sma_{period}'] = df['close'].rolling(window=period).mean()
                df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
                df[f'price_vs_sma_{period}'] = (df['close'] - df[f'sma_{period}']) / df[f'sma_{period}']
            
            # معالم الزخم
            df['roc_5'] = df['close'].pct_change(5)
            df['roc_10'] = df['close'].pct_change(10)
            df['momentum'] = df['close'] / df['close'].shift(10)
            
            # معالم التقلبات
            df['atr'] = self._calculate_atr(df)
            df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=20).mean()
            
            # معالم الحجم (محاكاة)
            if 'ticks' in df.columns:
                df['volume'] = pd.to_numeric(df['ticks'], errors='coerce')
            else:
                df['volume'] = df['price_range'] * 1000
            
            df['volume_sma'] = df['volume'].rolling(window=10).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            
            # معالم من المؤشرات الخارجية - تجاهل لتجنب مشكلة عدم التطابق
            # سنعتمد فقط على الميزات المحسوبة من البيانات التاريخية
            # if indicators:
            #     for key, value in indicators.items():
            #         if isinstance(value, (int, float)):
            #             df[f'indicator_{key}'] = value
            #         elif isinstance(value, dict):
            #             for sub_key, sub_value in value.items():
            #                 if isinstance(sub_value, (int, float)):
            #                     df[f'indicator_{key}_{sub_key}'] = sub_value
            
            # معالم الأنماط
            df['doji'] = self._detect_doji_pattern(df)
            df['hammer'] = self._detect_hammer_pattern(df)
            df['shooting_star'] = self._detect_shooting_star_pattern(df)
            
            # معالم الاتجاه
            df['trend_5'] = self._calculate_trend_strength(df, 5)
            df['trend_10'] = self._calculate_trend_strength(df, 10)
            df['trend_20'] = self._calculate_trend_strength(df, 20)
            
            # معالم إحصائية
            df['z_score'] = (df['close'] - df['close'].rolling(window=20).mean()) / df['close'].rolling(window=20).std()
            df['percentile_rank'] = df['close'].rolling(window=20).rank(pct=True)
            
            # معالم الوقت
            if 'time' in df.columns:
                df['hour'] = pd.to_datetime(df['time'], unit='s').dt.hour
                df['day_of_week'] = pd.to_datetime(df['time'], unit='s').dt.dayofweek
            
            # إزالة القيم المفقودة
            df = df.dropna()
            
            # اختيار المعالم النهائية
            feature_columns = [col for col in df.columns if col not in 
                             ['open', 'high', 'low', 'close', 'time', 'datetime', 'ticks']]
            
            return df[feature_columns]
            
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            return pd.DataFrame()
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """حساب Average True Range"""
        try:
            high_low = df['high'] - df['low']
            high_close = abs(df['high'] - df['close'].shift(1))
            low_close = abs(df['low'] - df['close'].shift(1))
            
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            return true_range.rolling(window=period).mean()
            
        except Exception:
            return pd.Series(0, index=df.index)
    
    def _detect_doji_pattern(self, df: pd.DataFrame) -> pd.Series:
        """كشف نمط الدوجي"""
        try:
            body_size = abs(df['close'] - df['open'])
            total_range = df['high'] - df['low']
            
            return (body_size / total_range < 0.1).astype(int)
            
        except Exception:
            return pd.Series(0, index=df.index)
    
    def _detect_hammer_pattern(self, df: pd.DataFrame) -> pd.Series:
        """كشف نمط المطرقة"""
        try:
            body_size = abs(df['close'] - df['open'])
            lower_shadow = np.minimum(df['open'], df['close']) - df['low']
            upper_shadow = df['high'] - np.maximum(df['open'], df['close'])
            
            hammer_condition = (lower_shadow > 2 * body_size) & (upper_shadow < body_size * 0.5)
            return hammer_condition.astype(int)
            
        except Exception:
            return pd.Series(0, index=df.index)
    
    def _detect_shooting_star_pattern(self, df: pd.DataFrame) -> pd.Series:
        """كشف نمط النجمة الساقطة"""
        try:
            body_size = abs(df['close'] - df['open'])
            lower_shadow = np.minimum(df['open'], df['close']) - df['low']
            upper_shadow = df['high'] - np.maximum(df['open'], df['close'])
            
            shooting_star_condition = (upper_shadow > 2 * body_size) & (lower_shadow < body_size * 0.5)
            return shooting_star_condition.astype(int)
            
        except Exception:
            return pd.Series(0, index=df.index)
    
    def _calculate_trend_strength(self, df: pd.DataFrame, period: int) -> pd.Series:
        """حساب قوة الاتجاه"""
        try:
            price_changes = df['close'].diff()
            positive_changes = (price_changes > 0).rolling(window=period).sum()
            total_changes = period
            
            return positive_changes / total_changes
            
        except Exception:
            return pd.Series(0.5, index=df.index)

    def _auto_train_models(self, features_df: pd.DataFrame, pair_name: str):
        """التدريب التلقائي للنماذج"""
        try:
            # تحميل البيانات التاريخية للتدريب
            historical_data = self._load_historical_training_data(pair_name)

            if len(historical_data) < 50:
                logger.info(f"🔄 Generating synthetic training data for {pair_name}")
                historical_data = self._generate_synthetic_training_data(features_df)

            if len(historical_data) < 30:
                logger.warning(f"⚠️ Insufficient data for training {pair_name}")
                return

            # إنشاء الأهداف للتدريب
            targets = self._create_training_targets_from_historical(historical_data)
            if targets is None or len(targets) == 0:
                return

            # تحضير البيانات للتدريب
            X, y_direction, y_confidence = self._prepare_training_data(historical_data, targets)

            if len(X) < 20:  # حد أدنى مخفض للتدريب
                logger.warning(f"⚠️ Very limited training data: {len(X)} samples")
                return

            # تدريب نموذج التنبؤ بالاتجاه
            self._train_direction_predictor(X, y_direction)

            # تدريب نموذج تقدير الثقة
            self._train_confidence_estimator(X, y_confidence)

            # حفظ النماذج
            self._save_models(pair_name)

            logger.info(f"✅ Models trained successfully for {pair_name} with {len(X)} samples")

        except Exception as e:
            logger.error(f"❌ Error in auto training: {e}")

    def _check_periodic_retraining(self, features_df: pd.DataFrame, pair_name: str):
        """فحص الحاجة لإعادة التدريب الدوري"""
        try:
            last_training = self.model_performance.get('last_training')

            # فحص إذا كان الوقت قد حان لإعادة التدريب
            should_retrain = False

            if last_training is None:
                should_retrain = True
                reason = "No previous training"
            else:
                # فحص الوقت المنقضي
                if isinstance(last_training, str):
                    last_training = datetime.fromisoformat(last_training.replace('Z', '+00:00'))

                hours_since_training = (datetime.now() - last_training).total_seconds() / 3600

                if hours_since_training > self.auto_training_config['retrain_interval_hours']:
                    should_retrain = True
                    reason = f"Time interval exceeded: {hours_since_training:.1f}h"

                # فحص الأداء
                current_accuracy = self.model_performance.get('accuracy', 0.5)
                if current_accuracy < self.auto_training_config['performance_threshold']:
                    should_retrain = True
                    reason = f"Low performance: {current_accuracy:.3f}"

            if should_retrain:
                logger.info(f"🔄 Periodic retraining triggered for {pair_name}: {reason}")
                self._auto_train_models(features_df, pair_name)

        except Exception as e:
            logger.error(f"Error in periodic retraining check: {e}")

    def _create_training_targets(self, features_df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """إنشاء الأهداف للتدريب"""
        try:
            # محاكاة الأهداف بناءً على الحركة المستقبلية
            if 'returns' not in features_df.columns:
                return None

            targets_df = pd.DataFrame(index=features_df.index)

            # الهدف: اتجاه الحركة في الفترات القادمة
            for horizon in [1, 2, 3, 5]:
                future_returns = features_df['returns'].shift(-horizon)
                targets_df[f'direction_{horizon}'] = (future_returns > 0).astype(int)
                targets_df[f'magnitude_{horizon}'] = abs(future_returns)

            # الهدف: قوة الحركة
            targets_df['strong_move'] = (abs(features_df['returns']) > features_df['volatility']).astype(int)

            return targets_df.dropna()

        except Exception as e:
            logger.error(f"Error creating training targets: {e}")
            return None

    def _prepare_training_data(self, features_df: pd.DataFrame, targets_df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """تحضير البيانات للتدريب"""
        try:
            # محاذاة البيانات
            common_index = features_df.index.intersection(targets_df.index)
            X = features_df.loc[common_index]
            y_targets = targets_df.loc[common_index]

            # اختيار المعالم الرقمية فقط
            numeric_features = X.select_dtypes(include=[np.number]).columns
            X = X[numeric_features]

            # معالجة القيم المفقودة
            X = X.fillna(X.mean())

            # تطبيع المعالم
            X_scaled = self.scalers['features'].fit_transform(X)

            # الأهداف
            y_direction = y_targets['direction_1'].values  # التنبؤ بالشمعة التالية
            y_confidence = y_targets['strong_move'].values  # قوة الحركة

            return X_scaled, y_direction, y_confidence

        except Exception as e:
            logger.error(f"Error preparing training data: {e}")
            return np.array([]), np.array([]), np.array([])

    def _train_direction_predictor(self, X: np.ndarray, y: np.ndarray):
        """تدريب نموذج التنبؤ بالاتجاه"""
        try:
            if len(X) == 0 or len(y) == 0:
                return

            # تعديل معاملات النموذج حسب حجم البيانات
            if len(X) < 50:
                n_estimators = 20
                max_depth = 5
            elif len(X) < 100:
                n_estimators = 50
                max_depth = 7
            else:
                n_estimators = 100
                max_depth = 10

            # تدريب نموذج Random Forest مع معاملات مرنة
            model = RandomForestClassifier(
                n_estimators=n_estimators,
                max_depth=max_depth,
                min_samples_split=max(2, len(X) // 20),
                min_samples_leaf=max(1, len(X) // 50),
                random_state=42
            )

            # تدريب النموذج
            if len(X) > 10:  # تقسيم البيانات فقط إذا كان لدينا بيانات كافية
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
                model.fit(X_train, y_train)

                # تقييم النموذج
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
            else:
                # تدريب على جميع البيانات إذا كانت قليلة
                model.fit(X, y)
                accuracy = 0.6  # افتراض دقة متوسطة

            self.models['direction_predictor'] = model
            self.model_performance['accuracy'] = accuracy
            self.model_performance['last_training'] = datetime.now()
            self.model_performance['samples_count'] = len(X)

            logger.info(f"✅ Direction predictor trained with {len(X)} samples, accuracy: {accuracy:.3f}")

        except Exception as e:
            logger.error(f"❌ Error training direction predictor: {e}")

    def _train_confidence_estimator(self, X: np.ndarray, y: np.ndarray):
        """تدريب نموذج تقدير الثقة"""
        try:
            if len(X) == 0 or len(y) == 0:
                return

            # تعديل معاملات النموذج حسب حجم البيانات
            if len(X) < 30:
                n_estimators = 10
                max_depth = 3
            elif len(X) < 100:
                n_estimators = 25
                max_depth = 4
            else:
                n_estimators = 50
                max_depth = 6

            # تدريب نموذج Gradient Boosting مع معاملات مرنة
            model = GradientBoostingClassifier(
                n_estimators=n_estimators,
                max_depth=max_depth,
                learning_rate=0.1,
                random_state=42
            )

            model.fit(X, y)
            self.models['confidence_estimator'] = model

            logger.info(f"✅ Confidence estimator trained with {len(X)} samples")

        except Exception as e:
            logger.error(f"❌ Error training confidence estimator: {e}")

    def _predict_direction(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """التنبؤ بالاتجاه"""
        try:
            if self.models['direction_predictor'] is None:
                # تدريب النموذج تلقائياً إذا لم يكن موجوداً
                logger.info("🤖 No trained model found, starting automatic training...")
                self._auto_train_models(features_df, "AUTO_TRAINING")

                # إذا فشل التدريب، استخدم تحليل بسيط مؤقتاً
                if self.models['direction_predictor'] is None:
                    return self._simple_direction_analysis(features_df)

            # تحضير المعالم للتنبؤ مع التعامل مع عدم التطابق
            numeric_features = features_df.select_dtypes(include=[np.number]).columns
            X = features_df[numeric_features].iloc[-1:].fillna(features_df[numeric_features].mean())

            # التعامل مع عدم تطابق أسماء الميزات
            if hasattr(self.scalers['features'], 'feature_names_in_'):
                expected_features = self.scalers['features'].feature_names_in_
                current_features = X.columns.tolist()

                # العثور على الميزات المشتركة
                common_features = [f for f in expected_features if f in current_features]

                if len(common_features) < len(expected_features) * 0.5:  # إذا كان أقل من 50% من الميزات متطابقة
                    logger.warning(f"Too few matching features: {len(common_features)}/{len(expected_features)}")
                    return self._simple_direction_analysis(features_df)

                # استخدام الميزات المشتركة فقط
                X = X[common_features]

                # إضافة ميزات مفقودة بقيم افتراضية
                for feature in expected_features:
                    if feature not in X.columns:
                        X[feature] = 0.0

                # إعادة ترتيب الأعمدة لتطابق ترتيب التدريب
                X = X[expected_features]

            # تطبيع المعالم
            try:
                X_scaled = self.scalers['features'].transform(X)
            except Exception as e:
                logger.warning(f"Scaling failed: {e}, using simple analysis")
                return self._simple_direction_analysis(features_df)

            # التنبؤ
            prediction = self.models['direction_predictor'].predict(X_scaled)[0]
            prediction_proba = self.models['direction_predictor'].predict_proba(X_scaled)[0]

            # تحويل إلى إشارة
            signal = "CALL" if prediction == 1 else "PUT"
            confidence = max(prediction_proba) * 100

            # تعديل الثقة بناءً على أداء النموذج
            model_accuracy = self.model_performance.get('accuracy', 0.5)
            adjusted_confidence = confidence * model_accuracy

            return {
                "signal": signal,
                "confidence": min(adjusted_confidence, 95),
                "prediction_proba": prediction_proba.tolist(),
                "model_accuracy": model_accuracy,
                "reason": f"AI prediction with {model_accuracy:.2f} accuracy"
            }

        except Exception as e:
            logger.error(f"Error in direction prediction: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _estimate_confidence(self, features_df: pd.DataFrame, direction_prediction: Dict[str, Any]) -> Dict[str, Any]:
        """تقدير الثقة"""
        try:
            if self.models['confidence_estimator'] is None:
                base_confidence = direction_prediction.get('confidence', 50)
                return {"confidence_boost": 0, "final_confidence": base_confidence}

            # تحضير المعالم
            numeric_features = features_df.select_dtypes(include=[np.number]).columns
            X = features_df[numeric_features].iloc[-1:].fillna(features_df[numeric_features].mean())
            X_scaled = self.scalers['features'].transform(X)

            # تقدير قوة الحركة
            strength_prediction = self.models['confidence_estimator'].predict_proba(X_scaled)[0]
            strength_score = max(strength_prediction)

            # تعديل الثقة
            base_confidence = direction_prediction.get('confidence', 50)
            confidence_boost = strength_score * 20  # زيادة تصل إلى 20 نقطة
            final_confidence = min(base_confidence + confidence_boost, 95)

            return {
                "confidence_boost": confidence_boost,
                "final_confidence": final_confidence,
                "strength_score": strength_score
            }

        except Exception as e:
            logger.error(f"Error in confidence estimation: {e}")
            return {"confidence_boost": 0, "final_confidence": 50}

    def _optimize_expiry_time(self, features_df: pd.DataFrame, direction_prediction: Dict[str, Any]) -> Dict[str, Any]:
        """تحسين زمن انتهاء الصفقة"""
        try:
            # تحليل التقلبات لتحديد الزمن الأمثل
            if 'volatility' in features_df.columns:
                current_volatility = features_df['volatility'].iloc[-1]
                avg_volatility = features_df['volatility'].mean()
                volatility_ratio = current_volatility / avg_volatility if avg_volatility > 0 else 1
            else:
                volatility_ratio = 1

            # تحليل قوة الإشارة
            signal_strength = direction_prediction.get('confidence', 50) / 100

            # تحديد الزمن الأمثل
            if volatility_ratio > 1.5:  # تقلبات عالية
                optimal_expiry = 1
            elif volatility_ratio > 1.2:  # تقلبات متوسطة
                optimal_expiry = 2
            elif signal_strength > 0.8:  # إشارة قوية
                optimal_expiry = 3
            else:  # حالة عادية
                optimal_expiry = 2

            return {
                "optimal_expiry": optimal_expiry,
                "volatility_ratio": volatility_ratio,
                "signal_strength": signal_strength,
                "reason": f"Optimized for volatility={volatility_ratio:.2f}, strength={signal_strength:.2f}"
            }

        except Exception as e:
            logger.error(f"Error in expiry optimization: {e}")
            return {"optimal_expiry": 2, "reason": "Default expiry"}

    def _analyze_complex_patterns(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل الأنماط المعقدة"""
        try:
            patterns_detected = []

            # نمط التقارب والتباعد
            if 'sma_5' in features_df.columns and 'sma_20' in features_df.columns:
                convergence_pattern = self._detect_convergence_pattern(features_df)
                if convergence_pattern['detected']:
                    patterns_detected.append(convergence_pattern)

            # نمط الكسر
            if 'z_score' in features_df.columns:
                breakout_pattern = self._detect_breakout_pattern(features_df)
                if breakout_pattern['detected']:
                    patterns_detected.append(breakout_pattern)

            # نمط الانعكاس المتقدم
            reversal_pattern = self._detect_advanced_reversal_pattern(features_df)
            if reversal_pattern['detected']:
                patterns_detected.append(reversal_pattern)

            if not patterns_detected:
                return {"signal": "NEUTRAL", "confidence": 0}

            # اختيار أقوى نمط
            best_pattern = max(patterns_detected, key=lambda x: x['confidence'])

            return {
                "signal": best_pattern['signal'],
                "confidence": best_pattern['confidence'],
                "pattern": best_pattern['pattern'],
                "expiry_minutes": best_pattern.get('expiry_minutes', 2)
            }

        except Exception as e:
            logger.error(f"Error in complex patterns analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _analyze_advanced_momentum(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل الزخم المتقدم"""
        try:
            momentum_signals = []

            # زخم السعر
            if 'roc_5' in features_df.columns and 'roc_10' in features_df.columns:
                price_momentum = self._calculate_price_momentum(features_df)
                momentum_signals.append(price_momentum)

            # زخم الحجم
            if 'volume_ratio' in features_df.columns:
                volume_momentum = self._calculate_volume_momentum(features_df)
                momentum_signals.append(volume_momentum)

            # زخم التقلبات
            if 'volatility_ratio' in features_df.columns:
                volatility_momentum = self._calculate_volatility_momentum(features_df)
                momentum_signals.append(volatility_momentum)

            if not momentum_signals:
                return {"signal": "NEUTRAL", "confidence": 0}

            # دمج إشارات الزخم
            call_signals = [s for s in momentum_signals if s.get('signal') == 'CALL']
            put_signals = [s for s in momentum_signals if s.get('signal') == 'PUT']

            if len(call_signals) > len(put_signals):
                avg_confidence = sum(s['confidence'] for s in call_signals) / len(call_signals)
                return {"signal": "CALL", "confidence": avg_confidence, "expiry_minutes": 2}
            elif len(put_signals) > len(call_signals):
                avg_confidence = sum(s['confidence'] for s in put_signals) / len(put_signals)
                return {"signal": "PUT", "confidence": avg_confidence, "expiry_minutes": 2}
            else:
                return {"signal": "NEUTRAL", "confidence": 0}

        except Exception as e:
            logger.error(f"Error in advanced momentum analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _combine_ai_analyses(self, direction_pred: Dict, confidence_est: Dict,
                           expiry_opt: Dict, pattern_analysis: Dict, momentum_analysis: Dict) -> Dict[str, Any]:
        """دمج جميع تحليلات الذكاء الاصطناعي"""
        try:
            analyses = [direction_pred, pattern_analysis, momentum_analysis]
            valid_analyses = [a for a in analyses if a.get('confidence', 0) > 0]

            if not valid_analyses:
                return self._create_neutral_result("No valid AI signals found")

            # تجميع الإشارات
            call_signals = [a for a in valid_analyses if a.get('signal') == 'CALL']
            put_signals = [a for a in valid_analyses if a.get('signal') == 'PUT']

            if len(call_signals) > len(put_signals):
                direction = "CALL"
                supporting_analyses = call_signals
            elif len(put_signals) > len(call_signals):
                direction = "PUT"
                supporting_analyses = put_signals
            else:
                return self._create_neutral_result("Conflicting AI signals")

            # حساب الثقة النهائية
            base_confidence = sum(a.get('confidence', 0) for a in supporting_analyses) / len(supporting_analyses)
            final_confidence = confidence_est.get('final_confidence', base_confidence)

            # تحديد زمن الصفقة
            optimal_expiry = expiry_opt.get('optimal_expiry', 2)

            # تطبيق مرشحات الجودة للذكاء الاصطناعي
            if final_confidence < 70:  # معيار أعلى للذكاء الاصطناعي
                return self._create_neutral_result("Low AI confidence")

            # تعزيز الثقة للنماذج المدربة جيداً
            model_accuracy = self.model_performance.get('accuracy', 0.5)
            if model_accuracy > 0.75:
                final_confidence *= 1.1

            return {
                "layer": self.name,
                "signal": direction,
                "confidence": min(final_confidence, 95),
                "supporting_analyses": len(supporting_analyses),
                "total_analyses": len(valid_analyses),
                "expiry_minutes": optimal_expiry,
                "model_performance": self.model_performance,
                "details": {
                    "direction_prediction": direction_pred,
                    "confidence_estimation": confidence_est,
                    "expiry_optimization": expiry_opt,
                    "pattern_analysis": pattern_analysis,
                    "momentum_analysis": momentum_analysis
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error combining AI analyses: {e}")
            return self._create_error_result(str(e))

    # ==================== دوال مساعدة ====================

    def _detect_convergence_pattern(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط التقارب والتباعد"""
        try:
            sma_5 = features_df['sma_5'].iloc[-5:]
            sma_20 = features_df['sma_20'].iloc[-5:]

            # حساب المسافة بين المتوسطات
            distance = abs(sma_5 - sma_20) / sma_20

            # البحث عن تقارب
            if distance.iloc[-1] < distance.iloc[0] * 0.5:
                # تحديد الاتجاه المحتمل للكسر
                if sma_5.iloc[-1] > sma_20.iloc[-1]:
                    signal = "CALL"
                else:
                    signal = "PUT"

                confidence = min((1 - distance.iloc[-1]) * 100, 80)

                return {
                    "detected": True,
                    "signal": signal,
                    "confidence": confidence,
                    "pattern": "CONVERGENCE",
                    "expiry_minutes": 3
                }

            return {"detected": False}

        except Exception:
            return {"detected": False}

    def _detect_breakout_pattern(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط الكسر"""
        try:
            z_scores = features_df['z_score'].iloc[-5:]
            current_z = z_scores.iloc[-1]

            # كسر قوي للأعلى أو للأسفل
            if abs(current_z) > 2.0:
                signal = "CALL" if current_z > 0 else "PUT"
                confidence = min(abs(current_z) * 25, 85)

                return {
                    "detected": True,
                    "signal": signal,
                    "confidence": confidence,
                    "pattern": "BREAKOUT",
                    "expiry_minutes": 1
                }

            return {"detected": False}

        except Exception:
            return {"detected": False}

    def _detect_advanced_reversal_pattern(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط الانعكاس المتقدم"""
        try:
            # دمج عدة مؤشرات للانعكاس
            reversal_score = 0

            # مؤشر الدوجي
            if 'doji' in features_df.columns and features_df['doji'].iloc[-1] == 1:
                reversal_score += 0.3

            # مؤشر المطرقة أو النجمة الساقطة
            if 'hammer' in features_df.columns and features_df['hammer'].iloc[-1] == 1:
                reversal_score += 0.4
                signal = "CALL"
            elif 'shooting_star' in features_df.columns and features_df['shooting_star'].iloc[-1] == 1:
                reversal_score += 0.4
                signal = "PUT"
            else:
                signal = "NEUTRAL"

            # مؤشر التشبع
            if 'z_score' in features_df.columns:
                z_score = features_df['z_score'].iloc[-1]
                if abs(z_score) > 1.5:
                    reversal_score += 0.3
                    if signal == "NEUTRAL":
                        signal = "PUT" if z_score > 0 else "CALL"

            if reversal_score > 0.6 and signal != "NEUTRAL":
                return {
                    "detected": True,
                    "signal": signal,
                    "confidence": min(reversal_score * 80, 85),
                    "pattern": "ADVANCED_REVERSAL",
                    "expiry_minutes": 2
                }

            return {"detected": False}

        except Exception:
            return {"detected": False}

    def _calculate_price_momentum(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """حساب زخم السعر"""
        try:
            roc_5 = features_df['roc_5'].iloc[-1]
            roc_10 = features_df['roc_10'].iloc[-1]

            # زخم قوي في نفس الاتجاه
            if roc_5 > 0.01 and roc_10 > 0.005:
                return {"signal": "CALL", "confidence": min(roc_5 * 1000, 75)}
            elif roc_5 < -0.01 and roc_10 < -0.005:
                return {"signal": "PUT", "confidence": min(abs(roc_5) * 1000, 75)}
            else:
                return {"signal": "NEUTRAL", "confidence": 0}

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0}

    def _calculate_volume_momentum(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """حساب زخم الحجم"""
        try:
            volume_ratio = features_df['volume_ratio'].iloc[-1]
            price_change = features_df['returns'].iloc[-1]

            # حجم عالي مع حركة سعرية
            if volume_ratio > 1.5 and abs(price_change) > 0.005:
                signal = "CALL" if price_change > 0 else "PUT"
                confidence = min(volume_ratio * 30, 70)
                return {"signal": signal, "confidence": confidence}
            else:
                return {"signal": "NEUTRAL", "confidence": 0}

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0}

    def _calculate_volatility_momentum(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """حساب زخم التقلبات"""
        try:
            volatility_ratio = features_df['volatility_ratio'].iloc[-1]
            trend_5 = features_df['trend_5'].iloc[-1]

            # تقلبات عالية مع اتجاه واضح
            if volatility_ratio > 1.3:
                if trend_5 > 0.6:
                    return {"signal": "CALL", "confidence": min(volatility_ratio * 40, 65)}
                elif trend_5 < 0.4:
                    return {"signal": "PUT", "confidence": min(volatility_ratio * 40, 65)}

            return {"signal": "NEUTRAL", "confidence": 0}

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0}

    def _load_saved_models(self):
        """تحميل النماذج المحفوظة"""
        try:
            models_dir = "data/models"
            if not os.path.exists(models_dir):
                os.makedirs(models_dir)
                return

            # تحميل نموذج التنبؤ بالاتجاه
            direction_model_path = os.path.join(models_dir, "direction_predictor.pkl")
            if os.path.exists(direction_model_path):
                with open(direction_model_path, 'rb') as f:
                    self.models['direction_predictor'] = pickle.load(f)

            # تحميل نموذج تقدير الثقة
            confidence_model_path = os.path.join(models_dir, "confidence_estimator.pkl")
            if os.path.exists(confidence_model_path):
                with open(confidence_model_path, 'rb') as f:
                    self.models['confidence_estimator'] = pickle.load(f)

            # تحميل معالجات البيانات
            scaler_path = os.path.join(models_dir, "feature_scaler.pkl")
            if os.path.exists(scaler_path):
                with open(scaler_path, 'rb') as f:
                    self.scalers['features'] = pickle.load(f)

            # تحميل معلومات الأداء
            performance_path = os.path.join(models_dir, "model_performance.json")
            if os.path.exists(performance_path):
                with open(performance_path, 'r') as f:
                    self.model_performance.update(json.load(f))

            logger.info("Saved models loaded successfully")

        except Exception as e:
            logger.error(f"Error loading saved models: {e}")

    def _save_models(self, pair_name: str):
        """حفظ النماذج"""
        try:
            models_dir = "data/models"
            if not os.path.exists(models_dir):
                os.makedirs(models_dir)

            # حفظ نموذج التنبؤ بالاتجاه
            if self.models['direction_predictor'] is not None:
                direction_model_path = os.path.join(models_dir, f"direction_predictor_{pair_name}.pkl")
                with open(direction_model_path, 'wb') as f:
                    pickle.dump(self.models['direction_predictor'], f)

            # حفظ نموذج تقدير الثقة
            if self.models['confidence_estimator'] is not None:
                confidence_model_path = os.path.join(models_dir, f"confidence_estimator_{pair_name}.pkl")
                with open(confidence_model_path, 'wb') as f:
                    pickle.dump(self.models['confidence_estimator'], f)

            # حفظ معالج البيانات
            scaler_path = os.path.join(models_dir, f"feature_scaler_{pair_name}.pkl")
            with open(scaler_path, 'wb') as f:
                pickle.dump(self.scalers['features'], f)

            # حفظ معلومات الأداء
            performance_path = os.path.join(models_dir, f"model_performance_{pair_name}.json")
            with open(performance_path, 'w') as f:
                json.dump(self.model_performance, f, indent=2, default=str)

            logger.info(f"Models saved successfully for {pair_name}")

        except Exception as e:
            logger.error(f"Error saving models: {e}")

    def _load_historical_training_data(self, pair_name: str) -> pd.DataFrame:
        """تحميل البيانات التاريخية للتدريب"""
        try:
            # تحميل البيانات التاريخية من الملفات
            file_path = f"data/historical/{pair_name}.json"
            if not os.path.exists(file_path):
                return pd.DataFrame()

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            candles = data.get('candles', [])
            if len(candles) < 20:
                return pd.DataFrame()

            # تحويل إلى DataFrame
            df = pd.DataFrame(candles)
            for col in ['open', 'high', 'low', 'close']:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # حساب الميزات الأساسية المتوافقة مع النظام الحالي
            df['returns'] = df['close'].pct_change()
            df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
            df['volatility'] = df['returns'].rolling(window=10).std()
            df['price_change'] = df['close'] - df['open']
            df['price_range'] = df['high'] - df['low']
            df['body_size'] = abs(df['close'] - df['open'])
            df['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
            df['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']

            # معالم المتوسطات المتحركة
            for period in [5, 10, 20]:
                df[f'sma_{period}'] = df['close'].rolling(window=period).mean()
                df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
                df[f'price_vs_sma_{period}'] = (df['close'] - df[f'sma_{period}']) / df[f'sma_{period}']

            # معالم الزخم
            df['roc_5'] = df['close'].pct_change(5)
            df['roc_10'] = df['close'].pct_change(10)
            df['momentum'] = df['close'] / df['close'].shift(10)

            # معالم التقلبات
            df['atr'] = self._calculate_atr(df)
            df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=20).mean()

            # معالم الحجم (محاكاة)
            if 'ticks' in df.columns:
                df['volume'] = pd.to_numeric(df['ticks'], errors='coerce')
            else:
                df['volume'] = df['price_range'] * 1000

            df['volume_sma'] = df['volume'].rolling(window=10).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']

            # معالم الأنماط
            df['doji'] = self._detect_doji_pattern(df)
            df['hammer'] = self._detect_hammer_pattern(df)
            df['shooting_star'] = self._detect_shooting_star_pattern(df)

            # معالم الاتجاه
            df['trend_5'] = self._calculate_trend_strength(df, 5)
            df['trend_10'] = self._calculate_trend_strength(df, 10)
            df['trend_20'] = self._calculate_trend_strength(df, 20)

            # معالم الوقت
            if 'time' in df.columns:
                df['datetime'] = pd.to_datetime(df['time'], unit='s')
                df['hour'] = df['datetime'].dt.hour
                df['day_of_week'] = df['datetime'].dt.dayofweek
                df['is_weekend'] = (df['datetime'].dt.dayofweek >= 5).astype(int)
            else:
                now = datetime.now()
                df['hour'] = now.hour
                df['day_of_week'] = now.weekday()
                df['is_weekend'] = int(now.weekday() >= 5)

            return df.dropna()

        except Exception as e:
            logger.error(f"Error loading historical training data: {e}")
            return pd.DataFrame()

    def _generate_synthetic_training_data(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """توليد بيانات تدريب اصطناعية متوافقة مع الميزات المطلوبة"""
        try:
            # إنشاء بيانات اصطناعية بناءً على أنماط السوق المعروفة
            np.random.seed(42)  # للحصول على نتائج قابلة للتكرار

            n_samples = 100
            synthetic_data = []

            for i in range(n_samples):
                # توليد أسعار أساسية
                base_price = np.random.uniform(0.5, 1.5)
                price_change = np.random.normal(0, 0.01)
                price_range = np.random.uniform(0.001, 0.02)

                # حساب الميزات المتوافقة مع النظام الحالي
                row = {
                    'returns': price_change,
                    'log_returns': np.log(1 + price_change),
                    'volatility': abs(price_change) + np.random.uniform(0.001, 0.02),
                    'price_change': price_change,
                    'price_range': price_range,
                    'body_size': abs(price_change),
                    'upper_shadow': np.random.uniform(0, price_range * 0.3),
                    'lower_shadow': np.random.uniform(0, price_range * 0.3),
                    'sma_5': base_price + np.random.normal(0, 0.01),
                    'sma_10': base_price + np.random.normal(0, 0.008),
                    'sma_20': base_price + np.random.normal(0, 0.005),
                    'ema_5': base_price + np.random.normal(0, 0.01),
                    'ema_10': base_price + np.random.normal(0, 0.008),
                    'ema_20': base_price + np.random.normal(0, 0.006),
                    'price_vs_sma_5': np.random.normal(0, 0.02),
                    'price_vs_sma_10': np.random.normal(0, 0.015),
                    'price_vs_sma_20': np.random.normal(0, 0.01),
                    'roc_5': np.random.normal(0, 0.02),
                    'roc_10': np.random.normal(0, 0.03),
                    'momentum': np.random.uniform(0.98, 1.02),
                    'atr': price_range,
                    'volatility_ratio': np.random.uniform(0.5, 2.0),
                    'volume': np.random.uniform(500, 2000),
                    'volume_sma': np.random.uniform(800, 1500),
                    'volume_ratio': np.random.uniform(0.5, 2.0),
                    'doji': np.random.choice([0, 1], p=[0.9, 0.1]),
                    'hammer': np.random.choice([0, 1], p=[0.95, 0.05]),
                    'shooting_star': np.random.choice([0, 1], p=[0.95, 0.05]),
                    'trend_5': np.random.uniform(0, 1),
                    'trend_10': np.random.uniform(0, 1),
                    'trend_20': np.random.uniform(0, 1),
                    'hour': np.random.randint(0, 24),
                    'day_of_week': np.random.randint(0, 7),
                    'is_weekend': np.random.choice([0, 1], p=[0.7, 0.3])
                }

                synthetic_data.append(row)

            df = pd.DataFrame(synthetic_data)

            # إضافة ميزات إحصائية
            df['skewness'] = np.random.normal(0, 0.5, n_samples)
            df['kurtosis'] = np.random.normal(3, 1, n_samples)
            df['zscore'] = np.random.normal(0, 1, n_samples)

            return df

        except Exception as e:
            logger.error(f"Error generating synthetic training data: {e}")
            return pd.DataFrame()

    def _create_training_targets_from_historical(self, historical_df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """إنشاء أهداف التدريب من البيانات التاريخية"""
        try:
            if 'returns' not in historical_df.columns:
                return None

            targets_df = pd.DataFrame(index=historical_df.index)

            # الهدف: اتجاه الحركة في الفترة القادمة
            future_returns = historical_df['returns'].shift(-1)
            targets_df['direction_1'] = (future_returns > 0).astype(int)

            # الهدف: قوة الحركة مع تنويع أكبر
            volatility = historical_df.get('volatility', historical_df['returns'].rolling(10).std())

            # إنشاء فئات متعددة لقوة الحركة
            abs_returns = abs(historical_df['returns'])
            volatility_threshold = volatility.fillna(volatility.mean())

            # تصنيف قوة الحركة إلى 3 فئات
            strong_move = np.where(abs_returns > volatility_threshold * 1.5, 2,  # حركة قوية جداً
                                 np.where(abs_returns > volatility_threshold, 1,  # حركة قوية
                                        0))  # حركة ضعيفة

            targets_df['strong_move'] = strong_move

            # إضافة تنويع إضافي للتأكد من وجود فئات متعددة
            if len(np.unique(targets_df['strong_move'])) < 2:
                # إضافة تنويع عشوائي
                random_indices = np.random.choice(len(targets_df), size=len(targets_df)//3, replace=False)
                targets_df.iloc[random_indices, targets_df.columns.get_loc('strong_move')] = 1

            return targets_df.dropna()

        except Exception as e:
            logger.error(f"Error creating training targets: {e}")
            return None

    def _simple_direction_analysis(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل بسيط للاتجاه كحل احتياطي"""
        try:
            # استخراج المؤشرات الأساسية
            latest_data = features_df.iloc[-1] if len(features_df) > 0 else pd.Series()

            # تحليل الزخم
            momentum_score = 0
            confidence = 25  # ثقة أساسية منخفضة

            # تحليل المتوسطات المتحركة
            if 'sma_5' in latest_data and 'sma_20' in latest_data:
                sma_5 = latest_data['sma_5']
                sma_20 = latest_data['sma_20']
                if sma_5 > sma_20:
                    momentum_score += 1
                    confidence += 15
                elif sma_5 < sma_20:
                    momentum_score -= 1
                    confidence += 15

            # تحليل RSI
            if 'rsi' in latest_data:
                rsi = latest_data['rsi']
                if rsi < 30:  # oversold
                    momentum_score += 1
                    confidence += 20
                elif rsi > 70:  # overbought
                    momentum_score -= 1
                    confidence += 20

            # تحديد الإشارة
            if momentum_score > 0:
                signal = "CALL"
            elif momentum_score < 0:
                signal = "PUT"
            else:
                signal = "NEUTRAL"
                confidence = max(confidence * 0.5, 15)

            return {
                "signal": signal,
                "confidence": min(confidence, 60),  # حد أقصى 60% للتحليل البسيط
                "momentum_score": momentum_score,
                "reason": "Simple fallback analysis (training in progress)"
            }

        except Exception as e:
            logger.error(f"Error in simple direction analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _create_error_result(self, error_msg: str) -> Dict[str, Any]:
        """إنشاء نتيجة خطأ"""
        return {
            "layer": self.name,
            "signal": "NEUTRAL",
            "confidence": 0,
            "error": error_msg,
            "timestamp": datetime.now().isoformat()
        }

    def _create_neutral_result(self, reason: str) -> Dict[str, Any]:
        """إنشاء نتيجة محايدة"""
        return {
            "layer": self.name,
            "signal": "NEUTRAL",
            "confidence": 0,
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        }
