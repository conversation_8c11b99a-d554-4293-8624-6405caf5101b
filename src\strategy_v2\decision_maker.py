"""
Strategy Decision Maker - متخذ القرارات الاستراتيجي
==================================================
الدماغ المركزي للاستراتيجية - يجمع ويحلل إشارات الطبقات الأربع
"""

import json
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

from .technical_layer import TechnicalAnalysisLayer
from .quantitative_layer import QuantitativeAnalysisLayer
from .behavioral_layer import BehavioralAnalysisLayer
from .ai_layer import AIAnalysisLayer

logger = logging.getLogger(__name__)

class StrategyDecisionMaker:
    """متخذ القرارات الاستراتيجي - الدماغ المركزي للاستراتيجية"""
    
    def __init__(self):
        """تهيئة متخذ القرارات"""
        self.name = "CANFX Strategy Decision Maker V2"
        self.version = "2.0.0"
        
        # تهيئة الطبقات الأربع
        self.technical_layer = TechnicalAnalysisLayer()
        self.quantitative_layer = QuantitativeAnalysisLayer()
        self.behavioral_layer = BehavioralAnalysisLayer()
        self.ai_layer = AIAnalysisLayer()
        
        # أوزان الطبقات (يمكن تعديلها ديناميكياً)
        self.layer_weights = {
            'technical': 0.25,
            'quantitative': 0.25,
            'behavioral': 0.25,
            'ai': 0.25
        }
        
        # معايير القرار - تم تخفيض العتبات لتحسين توليد الإشارات
        self.decision_criteria = {
            'min_confidence': 60.0,          # الحد الأدنى للثقة (مخفض من 80 إلى 60)
            'min_layer_agreement': 2,        # الحد الأدنى لاتفاق الطبقات (مخفض من 3 إلى 2)
            'max_conflicting_layers': 2,     # الحد الأقصى للطبقات المتضاربة (زيادة من 1 إلى 2)
            'confidence_boost_threshold': 75.0,  # عتبة تعزيز الثقة (مخفض من 85 إلى 75)
            'emergency_stop_threshold': 95.0     # عتبة الإيقاف الطارئ
        }
        
        # إحصائيات الأداء
        self.performance_stats = {
            'total_signals': 0,
            'successful_signals': 0,
            'win_rate': 0.0,
            'avg_confidence': 0.0,
            'layer_performance': {
                'technical': {'correct': 0, 'total': 0},
                'quantitative': {'correct': 0, 'total': 0},
                'behavioral': {'correct': 0, 'total': 0},
                'ai': {'correct': 0, 'total': 0}
            }
        }
        
        # ذاكرة الإشارات
        self.signal_history = []
        self.max_history_size = 1000
        
        logger.info(f"✅ {self.name} v{self.version} initialized with 4 analysis layers")
    
    def analyze_market(self, pair_name: str) -> Dict[str, Any]:
        """
        التحليل الشامل للسوق باستخدام الطبقات الأربع
        
        Args:
            pair_name: اسم زوج العملات
            
        Returns:
            القرار النهائي مع الإشارة والثقة وزمن الصفقة
        """
        try:
            analysis_start_time = datetime.now()
            logger.info(f"🔍 Starting comprehensive market analysis for {pair_name}")
            
            # تحليل الطبقات الأربع بشكل متوازي
            layer_results = self._analyze_all_layers(pair_name)
            
            # التحقق من صحة النتائج
            valid_results = self._validate_layer_results(layer_results)

            # تسجيل نتائج الطبقات للتشخيص
            logger.info(f"📊 Layer results summary:")
            for layer_name, result in layer_results.items():
                if 'error' not in result:
                    signal = result.get('signal', 'NEUTRAL')
                    confidence = result.get('confidence', 0)
                    logger.info(f"  {layer_name}: {signal} ({confidence:.1f}%)")
                else:
                    logger.warning(f"  {layer_name}: ERROR - {result.get('error', 'Unknown')}")

            if not valid_results:
                logger.warning(f"❌ No valid layer results for {pair_name}")
                return self._create_no_signal_result("No valid layer results", pair_name)
            
            # تحليل اتفاق الطبقات
            agreement_analysis = self._analyze_layer_agreement(valid_results)

            # حساب الثقة المركبة
            composite_confidence = self._calculate_composite_confidence(valid_results, agreement_analysis)

            # تحديد الإشارة النهائية مع تساهل أكبر
            final_signal = self._determine_final_signal_flexible(valid_results, agreement_analysis, composite_confidence)
            
            # تحسين زمن الصفقة
            optimized_expiry = self._optimize_trade_expiry(valid_results, final_signal)
            
            # تطبيق مرشحات الجودة النهائية مع مرونة أكبر
            quality_check = self._apply_quality_filters_flexible(final_signal, composite_confidence, agreement_analysis)

            if not quality_check['passed']:
                logger.info(f"❌ Signal rejected: {quality_check['reason']}")
                logger.info(f"📊 Analysis summary - Signal: {final_signal}, Confidence: {composite_confidence:.1f}%, Agreement: {agreement_analysis}")
                return self._create_no_signal_result(quality_check['reason'], pair_name)
            
            # إنشاء القرار النهائي
            final_decision = self._create_final_decision(
                pair_name,
                final_signal,
                composite_confidence,
                optimized_expiry,
                valid_results,
                agreement_analysis,
                analysis_start_time
            )
            
            # حفظ الإشارة في الذاكرة
            self._save_signal_to_history(final_decision)
            
            logger.info(f"✅ Analysis completed for {pair_name}")
            return final_decision
            
        except Exception as e:
            logger.error(f"Error in market analysis for {pair_name}: {e}")
            return self._create_error_result(str(e), pair_name)
    
    def _analyze_all_layers(self, pair_name: str) -> Dict[str, Any]:
        """تحليل جميع الطبقات"""
        try:
            results = {}
            
            # الطبقة الأولى: التحليل الفني
            try:
                results['technical'] = self.technical_layer.analyze_pair(pair_name)
                logger.debug(f"Technical analysis completed for {pair_name}")
            except Exception as e:
                logger.error(f"Technical analysis failed: {e}")
                results['technical'] = {'signal': 'NEUTRAL', 'confidence': 0, 'error': str(e)}
            
            # الطبقة الثانية: التحليل الكمي
            try:
                results['quantitative'] = self.quantitative_layer.analyze_pair(pair_name)
                logger.debug(f"Quantitative analysis completed for {pair_name}")
            except Exception as e:
                logger.error(f"Quantitative analysis failed: {e}")
                results['quantitative'] = {'signal': 'NEUTRAL', 'confidence': 0, 'error': str(e)}
            
            # الطبقة الثالثة: التحليل السلوكي
            try:
                results['behavioral'] = self.behavioral_layer.analyze_pair(pair_name)
                logger.debug(f"Behavioral analysis completed for {pair_name}")
            except Exception as e:
                logger.error(f"Behavioral analysis failed: {e}")
                results['behavioral'] = {'signal': 'NEUTRAL', 'confidence': 0, 'error': str(e)}
            
            # الطبقة الرابعة: الذكاء الاصطناعي
            try:
                results['ai'] = self.ai_layer.analyze_pair(pair_name)
                logger.debug(f"AI analysis completed for {pair_name}")
            except Exception as e:
                logger.error(f"AI analysis failed: {e}")
                results['ai'] = {'signal': 'NEUTRAL', 'confidence': 0, 'error': str(e)}
            
            return results
            
        except Exception as e:
            logger.error(f"Error analyzing all layers: {e}")
            return {}
    
    def _validate_layer_results(self, layer_results: Dict[str, Any]) -> Dict[str, Any]:
        """التحقق من صحة نتائج الطبقات"""
        try:
            valid_results = {}
            
            for layer_name, result in layer_results.items():
                if not isinstance(result, dict):
                    continue
                
                signal = result.get('signal', 'NEUTRAL')
                confidence = result.get('confidence', 0)
                
                # التحقق من صحة الإشارة
                if signal not in ['CALL', 'PUT', 'NEUTRAL']:
                    continue
                
                # التحقق من صحة الثقة
                if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 100:
                    continue
                
                # قبول الإشارات ذات الثقة المعقولة فقط
                if signal != 'NEUTRAL' and confidence >= 50:
                    valid_results[layer_name] = result
                elif signal == 'NEUTRAL':
                    valid_results[layer_name] = result
            
            return valid_results
            
        except Exception as e:
            logger.error(f"Error validating layer results: {e}")
            return {}
    
    def _analyze_layer_agreement(self, valid_results: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل اتفاق الطبقات"""
        try:
            signals = {}
            confidences = {}
            
            for layer_name, result in valid_results.items():
                signal = result.get('signal', 'NEUTRAL')
                confidence = result.get('confidence', 0)
                
                if signal != 'NEUTRAL':
                    signals[layer_name] = signal
                    confidences[layer_name] = confidence
            
            # تجميع الإشارات
            call_layers = [name for name, signal in signals.items() if signal == 'CALL']
            put_layers = [name for name, signal in signals.items() if signal == 'PUT']
            
            # حساب الاتفاق
            total_active_layers = len(signals)
            call_count = len(call_layers)
            put_count = len(put_layers)
            
            if call_count > put_count:
                majority_signal = 'CALL'
                majority_count = call_count
                minority_count = put_count
                supporting_layers = call_layers
            elif put_count > call_count:
                majority_signal = 'PUT'
                majority_count = put_count
                minority_count = call_count
                supporting_layers = put_layers
            else:
                majority_signal = 'NEUTRAL'
                majority_count = 0
                minority_count = 0
                supporting_layers = []
            
            # حساب نسبة الاتفاق
            agreement_ratio = majority_count / total_active_layers if total_active_layers > 0 else 0
            
            # حساب قوة الاتفاق
            agreement_strength = self._calculate_agreement_strength(
                supporting_layers, confidences, majority_count, total_active_layers
            )
            
            return {
                'majority_signal': majority_signal,
                'majority_count': majority_count,
                'minority_count': minority_count,
                'total_active_layers': total_active_layers,
                'agreement_ratio': agreement_ratio,
                'agreement_strength': agreement_strength,
                'supporting_layers': supporting_layers,
                'call_layers': call_layers,
                'put_layers': put_layers,
                'layer_confidences': confidences
            }
            
        except Exception as e:
            logger.error(f"Error analyzing layer agreement: {e}")
            return {
                'majority_signal': 'NEUTRAL',
                'agreement_ratio': 0,
                'agreement_strength': 0,
                'supporting_layers': []
            }

    def _calculate_agreement_strength(self, supporting_layers: List[str], confidences: Dict[str, float],
                                    majority_count: int, total_layers: int) -> float:
        """حساب قوة الاتفاق"""
        try:
            if not supporting_layers:
                return 0

            # قوة الاتفاق تعتمد على:
            # 1. عدد الطبقات المتفقة
            # 2. متوسط ثقة الطبقات المتفقة
            # 3. نسبة الاتفاق

            # متوسط ثقة الطبقات المتفقة
            avg_confidence = sum(confidences.get(layer, 0) for layer in supporting_layers) / len(supporting_layers)

            # نسبة الاتفاق
            agreement_ratio = majority_count / total_layers

            # قوة الاتفاق المركبة
            strength = (avg_confidence / 100) * agreement_ratio * (majority_count / 4)

            return min(strength, 1.0)

        except Exception:
            return 0

    def _calculate_composite_confidence(self, valid_results: Dict[str, Any],
                                      agreement_analysis: Dict[str, Any]) -> float:
        """حساب الثقة المركبة"""
        try:
            supporting_layers = agreement_analysis.get('supporting_layers', [])
            layer_confidences = agreement_analysis.get('layer_confidences', {})

            if not supporting_layers:
                return 0

            # حساب الثقة المرجحة
            total_weight = 0
            weighted_confidence = 0

            for layer in supporting_layers:
                confidence = layer_confidences.get(layer, 0)
                weight = self.layer_weights.get(layer, 0.25)

                weighted_confidence += confidence * weight
                total_weight += weight

            if total_weight == 0:
                return 0

            base_confidence = weighted_confidence / total_weight

            # تعديلات الثقة
            agreement_strength = agreement_analysis.get('agreement_strength', 0)
            agreement_ratio = agreement_analysis.get('agreement_ratio', 0)

            # تعزيز الثقة للاتفاق القوي
            if agreement_ratio >= 0.75:  # 3 من 4 طبقات أو أكثر
                base_confidence *= 1.1

            # تعزيز إضافي للاتفاق الكامل
            if agreement_ratio == 1.0:  # جميع الطبقات متفقة
                base_confidence *= 1.15

            # تطبيق قوة الاتفاق
            final_confidence = base_confidence * (1 + agreement_strength * 0.2)

            return min(final_confidence, 95)

        except Exception as e:
            logger.error(f"Error calculating composite confidence: {e}")
            return 0

    def _determine_final_signal(self, valid_results: Dict[str, Any],
                              agreement_analysis: Dict[str, Any],
                              composite_confidence: float) -> str:
        """تحديد الإشارة النهائية"""
        try:
            majority_signal = agreement_analysis.get('majority_signal', 'NEUTRAL')
            agreement_ratio = agreement_analysis.get('agreement_ratio', 0)

            # معايير قبول الإشارة
            min_confidence = self.decision_criteria['min_confidence']
            min_agreement = self.decision_criteria['min_layer_agreement']

            # التحقق من المعايير الأساسية
            if majority_signal == 'NEUTRAL':
                return 'NEUTRAL'

            if composite_confidence < min_confidence:
                return 'NEUTRAL'

            if agreement_analysis.get('majority_count', 0) < min_agreement:
                return 'NEUTRAL'

            # التحقق من التضارب
            minority_count = agreement_analysis.get('minority_count', 0)
            max_conflicting = self.decision_criteria['max_conflicting_layers']

            if minority_count > max_conflicting:
                return 'NEUTRAL'

            return majority_signal

        except Exception as e:
            logger.error(f"Error determining final signal: {e}")
            return 'NEUTRAL'

    def _determine_final_signal_flexible(self, valid_results: Dict[str, Any],
                                       agreement_analysis: Dict[str, Any],
                                       composite_confidence: float) -> str:
        """تحديد الإشارة النهائية مع مرونة أكبر"""
        try:
            majority_signal = agreement_analysis.get('majority_signal', 'NEUTRAL')
            total_active_layers = agreement_analysis.get('total_active_layers', 0)

            # إذا لم تكن هناك طبقات نشطة
            if total_active_layers == 0:
                return 'NEUTRAL'

            # إذا كان لدينا طبقة واحدة فقط بثقة جيدة
            if total_active_layers == 1:
                for layer_name, result in valid_results.items():
                    if result.get('confidence', 0) >= 45:  # عتبة منخفضة للطبقة الواحدة
                        signal = result.get('signal', 'NEUTRAL')
                        if signal != 'NEUTRAL':
                            logger.info(f"✅ Single layer signal accepted: {signal} from {layer_name}")
                            return signal
                return 'NEUTRAL'

            # إذا كان لدينا طبقتان متضاربتان، اختر الأقوى
            if total_active_layers == 2:
                call_layers = agreement_analysis.get('call_layers', [])
                put_layers = agreement_analysis.get('put_layers', [])

                if len(call_layers) == 1 and len(put_layers) == 1:
                    # مقارنة قوة الإشارات
                    call_confidence = valid_results[call_layers[0]].get('confidence', 0)
                    put_confidence = valid_results[put_layers[0]].get('confidence', 0)

                    # إذا كان الفرق كبير، اختر الأقوى
                    if abs(call_confidence - put_confidence) > 8:  # فرق 8% كافي
                        if call_confidence > put_confidence:
                            logger.info(f"✅ Stronger signal chosen: CALL ({call_confidence:.1f}% vs {put_confidence:.1f}%)")
                            return 'CALL'
                        else:
                            logger.info(f"✅ Stronger signal chosen: PUT ({put_confidence:.1f}% vs {call_confidence:.1f}%)")
                            return 'PUT'

            # للحالات العادية، استخدم المنطق الأصلي مع عتبات مخففة
            min_confidence = 50  # مخفض من 60
            min_agreement = 1     # مخفض من 2

            if majority_signal == 'NEUTRAL':
                return 'NEUTRAL'

            if composite_confidence < min_confidence:
                return 'NEUTRAL'

            majority_count = agreement_analysis.get('majority_count', 0)
            if majority_count < min_agreement:
                return 'NEUTRAL'

            logger.info(f"✅ Standard signal accepted: {majority_signal} with {composite_confidence:.1f}% confidence")
            return majority_signal

        except Exception as e:
            logger.error(f"Error in flexible signal determination: {e}")
            return 'NEUTRAL'

    def _optimize_trade_expiry(self, valid_results: Dict[str, Any], final_signal: str) -> int:
        """تحسين زمن انتهاء الصفقة"""
        try:
            if final_signal == 'NEUTRAL':
                return 2  # افتراضي

            expiry_times = []

            # جمع أزمنة الانتهاء من الطبقات
            for layer_name, result in valid_results.items():
                if result.get('signal') == final_signal:
                    expiry = result.get('expiry_minutes')
                    if expiry and isinstance(expiry, (int, float)) and 1 <= expiry <= 5:
                        expiry_times.append(int(expiry))

            if not expiry_times:
                return 2  # افتراضي

            # حساب الزمن الأمثل
            # استخدام الوسيط لتجنب القيم الشاذة
            optimal_expiry = int(np.median(expiry_times))

            # التأكد من أن الزمن ضمن النطاق المسموح
            return max(1, min(optimal_expiry, 5))

        except Exception as e:
            logger.error(f"Error optimizing trade expiry: {e}")
            return 2

    def _apply_quality_filters(self, final_signal: str, composite_confidence: float,
                             agreement_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تطبيق مرشحات الجودة النهائية"""
        try:
            if final_signal == 'NEUTRAL':
                return {'passed': False, 'reason': 'Neutral signal'}

            # مرشح الثقة الدنيا
            min_confidence = self.decision_criteria['min_confidence']
            if composite_confidence < min_confidence:
                return {
                    'passed': False,
                    'reason': f'Low confidence: {composite_confidence:.1f}% < {min_confidence}%'
                }

            # مرشح الاتفاق الأدنى
            min_layers = self.decision_criteria['min_layer_agreement']
            majority_count = agreement_analysis.get('majority_count', 0)
            if majority_count < min_layers:
                return {
                    'passed': False,
                    'reason': f'Insufficient layer agreement: {majority_count} < {min_layers}'
                }

            # مرشح التضارب الأقصى
            max_conflicting = self.decision_criteria['max_conflicting_layers']
            minority_count = agreement_analysis.get('minority_count', 0)
            if minority_count > max_conflicting:
                return {
                    'passed': False,
                    'reason': f'Too many conflicting layers: {minority_count} > {max_conflicting}'
                }

            # مرشح الجودة العالية
            if composite_confidence >= self.decision_criteria['confidence_boost_threshold']:
                agreement_ratio = agreement_analysis.get('agreement_ratio', 0)
                if agreement_ratio >= 0.75:  # 3 من 4 طبقات أو أكثر
                    return {'passed': True, 'reason': 'High quality signal', 'quality': 'HIGH'}

            return {'passed': True, 'reason': 'Standard quality signal', 'quality': 'STANDARD'}

        except Exception as e:
            logger.error(f"Error applying quality filters: {e}")
            return {'passed': False, 'reason': f'Filter error: {str(e)}'}

    def _apply_quality_filters_flexible(self, final_signal: str, composite_confidence: float,
                                      agreement_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تطبيق مرشحات الجودة النهائية مع مرونة أكبر"""
        try:
            if final_signal == 'NEUTRAL':
                return {'passed': False, 'reason': 'Neutral signal'}

            total_active_layers = agreement_analysis.get('total_active_layers', 0)

            # إذا كان لدينا طبقة واحدة فقط بثقة جيدة، اقبلها
            if total_active_layers == 1:
                layer_confidences = agreement_analysis.get('layer_confidences', {})
                max_confidence = max(layer_confidences.values()) if layer_confidences else 0

                if max_confidence >= 45:  # عتبة منخفضة للطبقة الواحدة
                    return {'passed': True, 'reason': 'Single strong layer signal', 'quality': 'MODERATE'}
                else:
                    return {'passed': False, 'reason': f'Single layer confidence too low: {max_confidence:.1f}%'}

            # إذا كان لدينا طبقتان متضاربتان
            if total_active_layers == 2:
                call_layers = agreement_analysis.get('call_layers', [])
                put_layers = agreement_analysis.get('put_layers', [])

                if len(call_layers) == 1 and len(put_layers) == 1:
                    layer_confidences = agreement_analysis.get('layer_confidences', {})
                    call_conf = layer_confidences.get(call_layers[0], 0)
                    put_conf = layer_confidences.get(put_layers[0], 0)

                    # إذا كان الفرق كبير، اقبل الأقوى
                    if abs(call_conf - put_conf) > 8:
                        stronger_conf = max(call_conf, put_conf)
                        if stronger_conf >= 50:
                            return {'passed': True, 'reason': f'Stronger signal wins: {stronger_conf:.1f}%', 'quality': 'MODERATE'}

                    return {'passed': False, 'reason': f'Conflicting signals: CALL({call_conf:.1f}%) vs PUT({put_conf:.1f}%)'}

            # للحالات العادية مع عتبات مخففة
            min_confidence = 45  # مخفض من 60
            if composite_confidence < min_confidence:
                return {'passed': False, 'reason': f'Low confidence: {composite_confidence:.1f}% < {min_confidence}%'}

            # مرشح الاتفاق المرن
            majority_count = agreement_analysis.get('majority_count', 0)
            if majority_count >= 2:  # طبقتان أو أكثر متفقتان
                return {'passed': True, 'reason': 'Multiple layers agreement', 'quality': 'HIGH'}
            elif majority_count >= 1 and composite_confidence >= 55:  # طبقة واحدة بثقة عالية
                return {'passed': True, 'reason': 'Single layer with high confidence', 'quality': 'MODERATE'}

            return {'passed': False, 'reason': f'Insufficient agreement: {majority_count} layers'}

        except Exception as e:
            logger.error(f"Error in flexible quality filters: {e}")
            return {'passed': False, 'reason': 'Filter error'}

    def _create_final_decision(self, pair_name: str, final_signal: str, composite_confidence: float,
                             optimized_expiry: int, valid_results: Dict[str, Any],
                             agreement_analysis: Dict[str, Any], analysis_start_time: datetime) -> Dict[str, Any]:
        """إنشاء القرار النهائي"""
        try:
            analysis_duration = (datetime.now() - analysis_start_time).total_seconds()

            decision = {
                # معلومات أساسية
                'strategy': self.name,
                'version': self.version,
                'pair': pair_name,
                'timestamp': datetime.now().isoformat(),
                'analysis_duration_seconds': round(analysis_duration, 3),

                # الإشارة النهائية
                'signal': final_signal,
                'confidence': round(composite_confidence, 1),
                'expiry_minutes': optimized_expiry,

                # تفاصيل الاتفاق
                'layer_agreement': {
                    'majority_signal': agreement_analysis.get('majority_signal'),
                    'supporting_layers': agreement_analysis.get('supporting_layers', []),
                    'agreement_ratio': round(agreement_analysis.get('agreement_ratio', 0), 2),
                    'agreement_strength': round(agreement_analysis.get('agreement_strength', 0), 2),
                    'total_active_layers': agreement_analysis.get('total_active_layers', 0)
                },

                # نتائج الطبقات
                'layer_results': {},

                # إحصائيات الأداء
                'performance_stats': self.performance_stats.copy(),

                # معايير القرار
                'decision_criteria': self.decision_criteria.copy()
            }

            # إضافة نتائج الطبقات مع تنظيف البيانات
            for layer_name, result in valid_results.items():
                cleaned_result = {
                    'signal': result.get('signal', 'NEUTRAL'),
                    'confidence': round(result.get('confidence', 0), 1),
                    'expiry_minutes': result.get('expiry_minutes', 2)
                }

                # إضافة تفاصيل إضافية إذا كانت متاحة
                if 'reason' in result:
                    cleaned_result['reason'] = result['reason']

                decision['layer_results'][layer_name] = cleaned_result

            return decision

        except Exception as e:
            logger.error(f"Error creating final decision: {e}")
            return self._create_error_result(str(e), pair_name)

    def _save_signal_to_history(self, decision: Dict[str, Any]):
        """حفظ الإشارة في الذاكرة"""
        try:
            # إضافة الإشارة للذاكرة
            self.signal_history.append(decision)

            # الحفاظ على حجم الذاكرة
            if len(self.signal_history) > self.max_history_size:
                self.signal_history = self.signal_history[-self.max_history_size:]

            # تحديث الإحصائيات
            self.performance_stats['total_signals'] += 1

            # حفظ في ملف
            self._save_signal_to_file(decision)

        except Exception as e:
            logger.error(f"Error saving signal to history: {e}")

    def _save_signal_to_file(self, decision: Dict[str, Any]):
        """حفظ الإشارة في ملف"""
        try:
            signals_dir = "data/signals"
            if not os.path.exists(signals_dir):
                os.makedirs(signals_dir)

            # اسم الملف بناءً على التاريخ
            date_str = datetime.now().strftime('%Y-%m-%d')
            file_path = os.path.join(signals_dir, f"signals_{date_str}.json")

            # قراءة الإشارات الموجودة
            signals = []
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        signals = json.load(f)
                except:
                    signals = []

            # إضافة الإشارة الجديدة
            signals.append(decision)

            # حفظ الملف
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(signals, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Error saving signal to file: {e}")

    def _create_no_signal_result(self, reason: str, pair_name: str) -> Dict[str, Any]:
        """إنشاء نتيجة عدم وجود إشارة"""
        return {
            'strategy': self.name,
            'version': self.version,
            'pair': pair_name,
            'timestamp': datetime.now().isoformat(),
            'signal': 'NEUTRAL',
            'confidence': 0,
            'reason': reason,
            'layer_agreement': {
                'supporting_layers': [],
                'agreement_ratio': 0,
                'total_active_layers': 0
            }
        }

    def _create_error_result(self, error_msg: str, pair_name: str) -> Dict[str, Any]:
        """إنشاء نتيجة خطأ"""
        return {
            'strategy': self.name,
            'version': self.version,
            'pair': pair_name,
            'timestamp': datetime.now().isoformat(),
            'signal': 'NEUTRAL',
            'confidence': 0,
            'error': error_msg,
            'layer_agreement': {
                'supporting_layers': [],
                'agreement_ratio': 0,
                'total_active_layers': 0
            }
        }

    def get_performance_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الأداء"""
        try:
            return {
                'strategy': self.name,
                'version': self.version,
                'performance_stats': self.performance_stats.copy(),
                'decision_criteria': self.decision_criteria.copy(),
                'layer_weights': self.layer_weights.copy(),
                'signal_history_size': len(self.signal_history),
                'last_update': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {'error': str(e)}

    def update_performance(self, trade_result: Dict[str, Any]):
        """تحديث إحصائيات الأداء"""
        try:
            if trade_result.get('outcome') == 'WIN':
                self.performance_stats['successful_signals'] += 1

            # حساب معدل النجاح
            total = self.performance_stats['total_signals']
            successful = self.performance_stats['successful_signals']

            if total > 0:
                self.performance_stats['win_rate'] = successful / total

            # تحديث أداء الطبقات
            layer_results = trade_result.get('layer_results', {})
            outcome = trade_result.get('outcome')

            for layer_name, result in layer_results.items():
                if layer_name in self.performance_stats['layer_performance']:
                    layer_stats = self.performance_stats['layer_performance'][layer_name]
                    layer_stats['total'] += 1

                    if outcome == 'WIN':
                        layer_stats['correct'] += 1

        except Exception as e:
            logger.error(f"Error updating performance: {e}")
