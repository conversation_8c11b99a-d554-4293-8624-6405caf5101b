"""
Quantitative Analysis Layer - الطبقة الثانية
===========================================
تحليل كمي متقدم باستخدام النماذج الرياضية والإحصائية
"""

import json
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

# محاولة استيراد المكتبات الاختيارية
try:
    from scipy import stats
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    stats = None
    print("⚠️ Warning: 'scipy' library not found. Some statistical tests will be disabled.")

try:
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import accuracy_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    StandardScaler = None
    accuracy_score = None
    print("⚠️ Warning: 'sklearn' library not found. Some ML features will be disabled.")

logger = logging.getLogger(__name__)

class QuantitativeAnalysisLayer:
    """الطبقة الثانية: التحليل الكمي المتقدم"""
    
    def __init__(self):
        """تهيئة طبقة التحليل الكمي"""
        self.name = "Quantitative Analysis Layer"
        self.weight = 0.25  # وزن هذه الطبقة في القرار النهائي
        
        # إعدادات التحليل الكمي
        self.z_score_threshold = 2.0
        self.volatility_window = 20
        self.correlation_window = 50
        self.probability_window = 100
        
        # تخزين البيانات التاريخية للتحليل
        self.historical_signals = []
        self.performance_metrics = {}
        
        logger.info(f"✅ {self.name} initialized")
    
    def analyze_pair(self, pair_name: str) -> Dict[str, Any]:
        """
        تحليل كمي شامل للزوج
        
        Args:
            pair_name: اسم زوج العملات
            
        Returns:
            نتيجة التحليل الكمي مع الإشارة والثقة وزمن الصفقة
        """
        try:
            # قراءة البيانات التاريخية
            historical_data = self._load_historical_data(pair_name)
            if not historical_data or len(historical_data) < 50:
                return self._create_error_result("Insufficient historical data for quantitative analysis")
            
            # قراءة بيانات المؤشرات
            indicators_data = self._load_indicators_data(pair_name)
            if not indicators_data:
                return self._create_error_result("No indicators data available")
            
            # تحويل البيانات إلى DataFrame
            df = self._prepare_dataframe(historical_data)
            
            # التحليلات الكمية
            z_score_analysis = self._calculate_z_score_signals(df)
            volatility_analysis = self._analyze_volatility_patterns(df)
            probability_analysis = self._calculate_probability_signals(df, indicators_data)
            correlation_analysis = self._analyze_correlation_patterns(df)
            statistical_analysis = self._perform_statistical_tests(df)
            
            # تحليل الأداء التاريخي
            performance_analysis = self._analyze_historical_performance(pair_name)
            
            # دمج جميع التحليلات
            final_signal = self._combine_quantitative_analyses(
                z_score_analysis,
                volatility_analysis,
                probability_analysis,
                correlation_analysis,
                statistical_analysis,
                performance_analysis
            )
            
            return final_signal
            
        except Exception as e:
            logger.error(f"Error in quantitative analysis for {pair_name}: {e}")
            return self._create_error_result(str(e))
    
    def _load_historical_data(self, pair_name: str) -> List[Dict[str, Any]]:
        """قراءة البيانات التاريخية"""
        try:
            file_path = f"data/historical/{pair_name}.json"
            if not os.path.exists(file_path):
                return []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            candles = data.get('candles', [])
            live_candle = data.get('live_candle')
            
            # إضافة الشمعة الحية إذا كانت متاحة
            if live_candle:
                candles.append(live_candle)
            
            return candles[-100:]  # آخر 100 شمعة
            
        except Exception as e:
            logger.error(f"Error loading historical data for {pair_name}: {e}")
            return []
    
    def _load_indicators_data(self, pair_name: str) -> Dict[str, Any]:
        """قراءة بيانات المؤشرات"""
        try:
            file_path = f"data/indicators/{pair_name}_indicators.json"
            if not os.path.exists(file_path):
                return {}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return data.get('indicators', {})
            
        except Exception as e:
            logger.error(f"Error loading indicators data for {pair_name}: {e}")
            return {}
    
    def _prepare_dataframe(self, candles: List[Dict[str, Any]]) -> pd.DataFrame:
        """تحضير DataFrame للتحليل"""
        try:
            df = pd.DataFrame(candles)
            
            # تحويل الأعمدة إلى أرقام
            numeric_columns = ['open', 'high', 'low', 'close', 'time']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # حساب المؤشرات الأساسية
            df['returns'] = df['close'].pct_change()
            df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
            df['volatility'] = df['returns'].rolling(window=20).std()
            df['price_change'] = df['close'] - df['open']
            df['price_range'] = df['high'] - df['low']
            df['body_size'] = abs(df['close'] - df['open'])
            
            # حساب المتوسطات المتحركة
            df['sma_5'] = df['close'].rolling(window=5).mean()
            df['sma_10'] = df['close'].rolling(window=10).mean()
            df['sma_20'] = df['close'].rolling(window=20).mean()
            
            return df.dropna()
            
        except Exception as e:
            logger.error(f"Error preparing dataframe: {e}")
            return pd.DataFrame()
    
    def _calculate_z_score_signals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """حساب إشارات Z-Score"""
        try:
            if len(df) < 20:
                return {"signal": "NEUTRAL", "confidence": 0, "reason": "Insufficient data for Z-Score"}
            
            # حساب Z-Score للأسعار
            current_price = df['close'].iloc[-1]
            price_mean = df['close'].rolling(window=20).mean().iloc[-1]
            price_std = df['close'].rolling(window=20).std().iloc[-1]
            
            if price_std == 0:
                return {"signal": "NEUTRAL", "confidence": 0, "reason": "Zero standard deviation"}
            
            price_z_score = (current_price - price_mean) / price_std
            
            # حساب Z-Score للعوائد
            current_return = df['returns'].iloc[-1]
            returns_mean = df['returns'].rolling(window=20).mean().iloc[-1]
            returns_std = df['returns'].rolling(window=20).std().iloc[-1]
            
            if returns_std == 0:
                returns_z_score = 0
            else:
                returns_z_score = (current_return - returns_mean) / returns_std
            
            # تحديد الإشارة
            signal = "NEUTRAL"
            confidence = 0
            expiry_minutes = 2
            
            # إشارات الانعكاس (Mean Reversion)
            if price_z_score > self.z_score_threshold:
                signal = "PUT"
                confidence = min(abs(price_z_score) * 25, 85)
                expiry_minutes = 3
            elif price_z_score < -self.z_score_threshold:
                signal = "CALL"
                confidence = min(abs(price_z_score) * 25, 85)
                expiry_minutes = 3
            
            # تعزيز الإشارة بناءً على Z-Score العوائد
            if abs(returns_z_score) > 1.5:
                if (returns_z_score > 0 and signal == "CALL") or (returns_z_score < 0 and signal == "PUT"):
                    confidence += 15
                else:
                    confidence -= 10
            
            return {
                "signal": signal,
                "confidence": max(min(confidence, 90), 0),
                "price_z_score": price_z_score,
                "returns_z_score": returns_z_score,
                "expiry_minutes": expiry_minutes,
                "reason": f"Z-Score analysis: price={price_z_score:.2f}, returns={returns_z_score:.2f}"
            }
            
        except Exception as e:
            logger.error(f"Error in Z-Score calculation: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}
    
    def _analyze_volatility_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل أنماط التقلبات"""
        try:
            if len(df) < 30:
                return {"signal": "NEUTRAL", "confidence": 0, "reason": "Insufficient data for volatility analysis"}
            
            # حساب التقلبات الحالية والتاريخية
            current_volatility = df['volatility'].iloc[-1]
            avg_volatility = df['volatility'].rolling(window=30).mean().iloc[-1]
            volatility_std = df['volatility'].rolling(window=30).std().iloc[-1]
            
            if volatility_std == 0:
                return {"signal": "NEUTRAL", "confidence": 0, "reason": "Zero volatility deviation"}
            
            # حساب نسبة التقلبات
            volatility_ratio = current_volatility / avg_volatility if avg_volatility > 0 else 1
            volatility_z_score = (current_volatility - avg_volatility) / volatility_std
            
            # تحليل ATR (Average True Range)
            df['tr'] = np.maximum(
                df['high'] - df['low'],
                np.maximum(
                    abs(df['high'] - df['close'].shift(1)),
                    abs(df['low'] - df['close'].shift(1))
                )
            )
            current_atr = df['tr'].rolling(window=14).mean().iloc[-1]
            avg_atr = df['tr'].rolling(window=30).mean().iloc[-1]
            atr_ratio = current_atr / avg_atr if avg_atr > 0 else 1
            
            # تحديد الإشارة
            signal = "NEUTRAL"
            confidence = 0
            expiry_minutes = 2
            
            # تقلبات عالية = فرص أكبر للحركة
            if volatility_ratio > 1.5 and atr_ratio > 1.3:
                # تحديد الاتجاه بناءً على الزخم الحالي
                recent_returns = df['returns'].tail(5).mean()
                if recent_returns > 0:
                    signal = "CALL"
                else:
                    signal = "PUT"
                confidence = min(volatility_ratio * 30, 80)
                expiry_minutes = 1  # تقلبات عالية = وقت قصير
            
            # تقلبات منخفضة = انتظار الاختراق
            elif volatility_ratio < 0.7 and atr_ratio < 0.8:
                # البحث عن إشارات الاختراق
                price_position = self._get_price_position_in_range(df)
                if price_position == "HIGH":
                    signal = "PUT"
                    confidence = 60
                    expiry_minutes = 3
                elif price_position == "LOW":
                    signal = "CALL"
                    confidence = 60
                    expiry_minutes = 3
            
            return {
                "signal": signal,
                "confidence": confidence,
                "volatility_ratio": volatility_ratio,
                "atr_ratio": atr_ratio,
                "volatility_z_score": volatility_z_score,
                "expiry_minutes": expiry_minutes,
                "reason": f"Volatility analysis: ratio={volatility_ratio:.2f}, ATR={atr_ratio:.2f}"
            }
            
        except Exception as e:
            logger.error(f"Error in volatility analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}
    
    def _calculate_probability_signals(self, df: pd.DataFrame, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """حساب إشارات الاحتمالية"""
        try:
            if len(df) < 50:
                return {"signal": "NEUTRAL", "confidence": 0, "reason": "Insufficient data for probability analysis"}
            
            # تحليل احتمالية النجاح بناءً على الظروف الحالية
            current_conditions = self._extract_current_conditions(df, indicators)
            historical_success_rate = self._calculate_historical_success_rate(df, current_conditions)
            
            # تحليل الاحتمالية الشرطية
            conditional_probabilities = self._calculate_conditional_probabilities(df)
            
            # تحليل نمط التكرار
            pattern_probability = self._analyze_pattern_repetition(df)
            
            # حساب الاحتمالية المركبة
            combined_probability = self._combine_probabilities(
                historical_success_rate,
                conditional_probabilities,
                pattern_probability
            )
            
            # تحديد الإشارة
            signal = "NEUTRAL"
            confidence = 0
            expiry_minutes = 2
            
            if combined_probability['call_probability'] > 0.65:
                signal = "CALL"
                confidence = min(combined_probability['call_probability'] * 100, 90)
                expiry_minutes = combined_probability.get('optimal_expiry', 2)
            elif combined_probability['put_probability'] > 0.65:
                signal = "PUT"
                confidence = min(combined_probability['put_probability'] * 100, 90)
                expiry_minutes = combined_probability.get('optimal_expiry', 2)
            
            return {
                "signal": signal,
                "confidence": confidence,
                "call_probability": combined_probability['call_probability'],
                "put_probability": combined_probability['put_probability'],
                "historical_success_rate": historical_success_rate,
                "expiry_minutes": expiry_minutes,
                "reason": f"Probability analysis: CALL={combined_probability['call_probability']:.2f}, PUT={combined_probability['put_probability']:.2f}"
            }
            
        except Exception as e:
            logger.error(f"Error in probability calculation: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _analyze_correlation_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل أنماط الارتباط"""
        try:
            if len(df) < 30:
                return {"signal": "NEUTRAL", "confidence": 0}

            # حساب الارتباط بين المؤشرات المختلفة
            correlations = {}

            # ارتباط السعر مع المتوسطات المتحركة
            if 'sma_10' in df.columns and 'sma_20' in df.columns:
                price_sma_corr = df['close'].tail(20).corr(df['sma_10'].tail(20))
                correlations['price_sma'] = price_sma_corr

            # ارتباط العوائد مع التقلبات
            if 'returns' in df.columns and 'volatility' in df.columns:
                returns_vol_corr = df['returns'].tail(20).corr(df['volatility'].tail(20))
                correlations['returns_volatility'] = returns_vol_corr

            # ارتباط حجم الجسم مع المدى
            if 'body_size' in df.columns and 'price_range' in df.columns:
                body_range_corr = df['body_size'].tail(20).corr(df['price_range'].tail(20))
                correlations['body_range'] = body_range_corr

            # تحليل قوة الارتباطات
            strong_correlations = {k: v for k, v in correlations.items() if abs(v) > 0.7}

            signal = "NEUTRAL"
            confidence = 0

            if strong_correlations:
                # استخدام الارتباطات القوية لتحديد الإشارة
                avg_correlation = sum(strong_correlations.values()) / len(strong_correlations)

                if avg_correlation > 0.7:
                    signal = "CALL"
                    confidence = min(abs(avg_correlation) * 60, 75)
                elif avg_correlation < -0.7:
                    signal = "PUT"
                    confidence = min(abs(avg_correlation) * 60, 75)

            return {
                "signal": signal,
                "confidence": confidence,
                "correlations": correlations,
                "strong_correlations": strong_correlations,
                "expiry_minutes": 2
            }

        except Exception as e:
            logger.error(f"Error in correlation analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _perform_statistical_tests(self, df: pd.DataFrame) -> Dict[str, Any]:
        """إجراء الاختبارات الإحصائية"""
        try:
            if len(df) < 30:
                return {"signal": "NEUTRAL", "confidence": 0}

            results = {}

            # اختبار الحالة الطبيعية للعوائد
            if 'returns' in df.columns:
                returns = df['returns'].dropna()
                if len(returns) > 10:
                    # Shapiro-Wilk test for normality
                    try:
                        stat, p_value = stats.shapiro(returns.tail(20))
                        results['normality_test'] = {
                            'statistic': stat,
                            'p_value': p_value,
                            'is_normal': p_value > 0.05
                        }
                    except Exception:
                        results['normality_test'] = {'is_normal': False}

            # اختبار الاتجاه (Trend Test)
            if 'close' in df.columns:
                prices = df['close'].tail(20)
                if len(prices) > 10:
                    # Mann-Kendall trend test (simplified)
                    try:
                        trend_stat = self._mann_kendall_test(prices)
                        results['trend_test'] = trend_stat
                    except Exception:
                        results['trend_test'] = {'trend': 'NEUTRAL', 'significance': 0}

            # اختبار الاستقرارية (Stationarity)
            if 'returns' in df.columns:
                returns = df['returns'].dropna()
                if len(returns) > 15:
                    try:
                        stationarity = self._check_stationarity(returns)
                        results['stationarity_test'] = stationarity
                    except Exception:
                        results['stationarity_test'] = {'is_stationary': True}

            # تحديد الإشارة بناءً على الاختبارات
            signal = "NEUTRAL"
            confidence = 0

            # إشارة من اختبار الاتجاه
            if 'trend_test' in results:
                trend_info = results['trend_test']
                if trend_info.get('significance', 0) > 0.7:
                    if trend_info.get('trend') == 'INCREASING':
                        signal = "CALL"
                        confidence = trend_info['significance'] * 70
                    elif trend_info.get('trend') == 'DECREASING':
                        signal = "PUT"
                        confidence = trend_info['significance'] * 70

            return {
                "signal": signal,
                "confidence": min(confidence, 85),
                "statistical_tests": results,
                "expiry_minutes": 3
            }

        except Exception as e:
            logger.error(f"Error in statistical tests: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _analyze_historical_performance(self, pair_name: str) -> Dict[str, Any]:
        """تحليل الأداء التاريخي"""
        try:
            # محاولة قراءة سجل الأداء التاريخي
            performance_file = f"data/performance/{pair_name}_performance.json"

            if os.path.exists(performance_file):
                with open(performance_file, 'r', encoding='utf-8') as f:
                    performance_data = json.load(f)
            else:
                performance_data = {
                    'total_signals': 0,
                    'successful_signals': 0,
                    'win_rate': 0.5,
                    'avg_confidence': 50,
                    'best_time_frames': [2, 3],
                    'best_conditions': []
                }

            # حساب معدل النجاح
            win_rate = performance_data.get('win_rate', 0.5)
            total_signals = performance_data.get('total_signals', 0)

            # تحديد الإشارة بناءً على الأداء التاريخي
            signal = "NEUTRAL"
            confidence = 0

            if total_signals > 10:  # لدينا بيانات كافية
                if win_rate > 0.7:
                    # أداء جيد تاريخياً
                    confidence = min(win_rate * 80, 85)

                    # تحديد الاتجاه بناءً على الظروف الحالية
                    best_conditions = performance_data.get('best_conditions', [])
                    if best_conditions:
                        # استخدام أفضل الظروف التاريخية
                        signal = self._determine_signal_from_conditions(best_conditions)

                elif win_rate < 0.4:
                    # أداء ضعيف تاريخياً - تجنب التداول
                    confidence = 0

            # تحديد أفضل إطار زمني
            best_time_frames = performance_data.get('best_time_frames', [2, 3])
            optimal_expiry = best_time_frames[0] if best_time_frames else 2

            return {
                "signal": signal,
                "confidence": confidence,
                "win_rate": win_rate,
                "total_signals": total_signals,
                "optimal_expiry": optimal_expiry,
                "expiry_minutes": optimal_expiry,
                "reason": f"Historical performance: {win_rate:.2f} win rate over {total_signals} signals"
            }

        except Exception as e:
            logger.error(f"Error in historical performance analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}

    def _combine_quantitative_analyses(self, *analyses) -> Dict[str, Any]:
        """دمج جميع التحليلات الكمية"""
        try:
            valid_analyses = [a for a in analyses if a.get('confidence', 0) > 0]

            if not valid_analyses:
                return self._create_neutral_result("No valid quantitative signals found")

            # تجميع الإشارات
            call_signals = [a for a in valid_analyses if a.get('signal') == 'CALL']
            put_signals = [a for a in valid_analyses if a.get('signal') == 'PUT']

            if len(call_signals) > len(put_signals):
                direction = "CALL"
                supporting_analyses = call_signals
            elif len(put_signals) > len(call_signals):
                direction = "PUT"
                supporting_analyses = put_signals
            else:
                return self._create_neutral_result("Conflicting quantitative signals")

            # حساب الثقة المرجحة
            total_weight = 0
            weighted_confidence = 0

            for analysis in supporting_analyses:
                weight = self._get_analysis_weight(analysis)
                confidence = analysis.get('confidence', 0)
                weighted_confidence += confidence * weight
                total_weight += weight

            if total_weight > 0:
                final_confidence = weighted_confidence / total_weight
            else:
                final_confidence = sum(a.get('confidence', 0) for a in supporting_analyses) / len(supporting_analyses)

            # تحديد زمن الصفقة الأمثل
            expiry_times = [a.get('expiry_minutes', 2) for a in supporting_analyses if 'expiry_minutes' in a]
            optimal_expiry = int(np.median(expiry_times)) if expiry_times else 2

            # تطبيق مرشحات الجودة - تم تخفيض العتبة
            if final_confidence < 45:
                return self._create_neutral_result("Low quantitative confidence")

            if len(supporting_analyses) < 2:
                final_confidence *= 0.85  # تقليل الثقة للدعم المحدود

            return {
                "layer": self.name,
                "signal": direction,
                "confidence": min(final_confidence, 95),
                "supporting_analyses": len(supporting_analyses),
                "total_analyses": len(valid_analyses),
                "expiry_minutes": optimal_expiry,
                "details": {
                    "z_score": analyses[0] if len(analyses) > 0 else {},
                    "volatility": analyses[1] if len(analyses) > 1 else {},
                    "probability": analyses[2] if len(analyses) > 2 else {},
                    "correlation": analyses[3] if len(analyses) > 3 else {},
                    "statistical": analyses[4] if len(analyses) > 4 else {},
                    "performance": analyses[5] if len(analyses) > 5 else {}
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error combining quantitative analyses: {e}")
            return self._create_error_result(str(e))

    # ==================== دوال مساعدة ====================

    def _get_price_position_in_range(self, df: pd.DataFrame) -> str:
        """تحديد موقع السعر في المدى"""
        try:
            recent_data = df.tail(20)
            current_price = recent_data['close'].iloc[-1]
            high_20 = recent_data['high'].max()
            low_20 = recent_data['low'].min()

            if high_20 == low_20:
                return "MIDDLE"

            position = (current_price - low_20) / (high_20 - low_20)

            if position > 0.8:
                return "HIGH"
            elif position < 0.2:
                return "LOW"
            else:
                return "MIDDLE"

        except Exception:
            return "MIDDLE"

    def _extract_current_conditions(self, df: pd.DataFrame, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """استخراج الظروف الحالية للسوق"""
        try:
            conditions = {}

            # ظروف السعر
            current_price = df['close'].iloc[-1]
            sma_20 = df['sma_20'].iloc[-1] if 'sma_20' in df.columns else current_price
            conditions['price_above_sma20'] = current_price > sma_20

            # ظروف التقلبات
            current_volatility = df['volatility'].iloc[-1] if 'volatility' in df.columns else 0
            avg_volatility = df['volatility'].mean() if 'volatility' in df.columns else 0
            conditions['high_volatility'] = current_volatility > avg_volatility * 1.2

            # ظروف الاتجاه
            recent_returns = df['returns'].tail(5).mean() if 'returns' in df.columns else 0
            conditions['bullish_momentum'] = recent_returns > 0

            # ظروف المؤشرات
            rsi = indicators.get('RSI14', 50)
            conditions['rsi_oversold'] = rsi < 30
            conditions['rsi_overbought'] = rsi > 70

            return conditions

        except Exception as e:
            logger.error(f"Error extracting conditions: {e}")
            return {}

    def _calculate_historical_success_rate(self, df: pd.DataFrame, conditions: Dict[str, Any]) -> float:
        """حساب معدل النجاح التاريخي للظروف المشابهة"""
        try:
            # هذه دالة مبسطة - في التطبيق الحقيقي نحتاج قاعدة بيانات للصفقات التاريخية

            # محاكاة معدل النجاح بناءً على الظروف
            success_rate = 0.5  # معدل أساسي

            # تعديل بناءً على الظروف
            if conditions.get('high_volatility', False):
                success_rate += 0.1  # التقلبات العالية تزيد الفرص

            if conditions.get('rsi_oversold', False) or conditions.get('rsi_overbought', False):
                success_rate += 0.15  # مناطق التشبع تزيد فرص الانعكاس

            if conditions.get('bullish_momentum', False) and conditions.get('price_above_sma20', False):
                success_rate += 0.1  # اتجاه واضح

            return min(success_rate, 0.9)

        except Exception:
            return 0.5

    def _calculate_conditional_probabilities(self, df: pd.DataFrame) -> Dict[str, float]:
        """حساب الاحتماليات الشرطية"""
        try:
            if len(df) < 20:
                return {'call_prob': 0.5, 'put_prob': 0.5}

            # حساب احتمالية الارتفاع/الانخفاض بناءً على الظروف الحالية
            recent_data = df.tail(20)

            # احتمالية بناءً على الشموع السابقة
            bullish_candles = len(recent_data[recent_data['close'] > recent_data['open']])
            bearish_candles = len(recent_data[recent_data['close'] < recent_data['open']])

            total_candles = bullish_candles + bearish_candles
            if total_candles == 0:
                return {'call_prob': 0.5, 'put_prob': 0.5}

            # احتمالية أساسية
            base_call_prob = bullish_candles / total_candles
            base_put_prob = bearish_candles / total_candles

            # تعديل بناءً على الزخم
            if 'returns' in df.columns:
                recent_momentum = df['returns'].tail(3).mean()
                momentum_factor = min(abs(recent_momentum) * 100, 0.2)

                if recent_momentum > 0:
                    base_call_prob += momentum_factor
                    base_put_prob -= momentum_factor
                else:
                    base_put_prob += momentum_factor
                    base_call_prob -= momentum_factor

            # تطبيع الاحتماليات
            total_prob = base_call_prob + base_put_prob
            if total_prob > 0:
                base_call_prob /= total_prob
                base_put_prob /= total_prob

            return {
                'call_prob': max(min(base_call_prob, 0.9), 0.1),
                'put_prob': max(min(base_put_prob, 0.9), 0.1)
            }

        except Exception:
            return {'call_prob': 0.5, 'put_prob': 0.5}

    def _analyze_pattern_repetition(self, df: pd.DataFrame) -> Dict[str, float]:
        """تحليل تكرار الأنماط"""
        try:
            if len(df) < 10:
                return {'call_prob': 0.5, 'put_prob': 0.5}

            # البحث عن أنماط متكررة في آخر 10 شموع
            patterns = []

            for i in range(len(df) - 3):
                pattern = []
                for j in range(3):  # نمط من 3 شموع
                    candle = df.iloc[i + j]
                    if candle['close'] > candle['open']:
                        pattern.append('BULL')
                    else:
                        pattern.append('BEAR')
                patterns.append(tuple(pattern))

            # البحث عن النمط الحالي
            current_pattern = []
            for i in range(max(0, len(df) - 3), len(df)):
                candle = df.iloc[i]
                if candle['close'] > candle['open']:
                    current_pattern.append('BULL')
                else:
                    current_pattern.append('BEAR')

            if len(current_pattern) < 2:
                return {'call_prob': 0.5, 'put_prob': 0.5}

            current_pattern = tuple(current_pattern[-2:])  # آخر شمعتين

            # البحث عن تكرارات مشابهة
            similar_patterns = 0
            bullish_outcomes = 0

            for i in range(len(patterns) - 1):
                if patterns[i][:2] == current_pattern:
                    similar_patterns += 1
                    # فحص النتيجة التالية
                    next_candle_idx = i + 3
                    if next_candle_idx < len(df):
                        next_candle = df.iloc[next_candle_idx]
                        if next_candle['close'] > next_candle['open']:
                            bullish_outcomes += 1

            if similar_patterns == 0:
                return {'call_prob': 0.5, 'put_prob': 0.5}

            call_prob = bullish_outcomes / similar_patterns
            put_prob = 1 - call_prob

            return {
                'call_prob': call_prob,
                'put_prob': put_prob,
                'pattern_frequency': similar_patterns
            }

        except Exception:
            return {'call_prob': 0.5, 'put_prob': 0.5}

    def _combine_probabilities(self, historical_rate: float, conditional_probs: Dict[str, float],
                              pattern_probs: Dict[str, float]) -> Dict[str, Any]:
        """دمج الاحتماليات المختلفة"""
        try:
            # أوزان مختلفة للمصادر المختلفة
            weights = {
                'historical': 0.3,
                'conditional': 0.4,
                'pattern': 0.3
            }

            # حساب الاحتمالية المرجحة للـ CALL
            call_prob = (
                historical_rate * weights['historical'] +
                conditional_probs.get('call_prob', 0.5) * weights['conditional'] +
                pattern_probs.get('call_prob', 0.5) * weights['pattern']
            )

            put_prob = 1 - call_prob

            # تحديد الإطار الزمني الأمثل
            optimal_expiry = 2

            # إذا كانت الأنماط قوية، استخدم وقت أقصر
            pattern_frequency = pattern_probs.get('pattern_frequency', 0)
            if pattern_frequency > 3:
                optimal_expiry = 1
            elif historical_rate > 0.7:
                optimal_expiry = 3

            return {
                'call_probability': call_prob,
                'put_probability': put_prob,
                'optimal_expiry': optimal_expiry,
                'confidence_level': abs(call_prob - 0.5) * 2  # مدى البعد عن 50%
            }

        except Exception:
            return {
                'call_probability': 0.5,
                'put_probability': 0.5,
                'optimal_expiry': 2,
                'confidence_level': 0
            }

    def _mann_kendall_test(self, data: pd.Series) -> Dict[str, Any]:
        """اختبار Mann-Kendall للاتجاه (مبسط)"""
        try:
            n = len(data)
            if n < 3:
                return {'trend': 'NEUTRAL', 'significance': 0}

            # حساب إحصائية Mann-Kendall
            s = 0
            for i in range(n - 1):
                for j in range(i + 1, n):
                    if data.iloc[j] > data.iloc[i]:
                        s += 1
                    elif data.iloc[j] < data.iloc[i]:
                        s -= 1

            # حساب التباين
            var_s = n * (n - 1) * (2 * n + 5) / 18

            if var_s == 0:
                return {'trend': 'NEUTRAL', 'significance': 0}

            # حساب Z-score
            if s > 0:
                z = (s - 1) / np.sqrt(var_s)
                trend = 'INCREASING'
            elif s < 0:
                z = (s + 1) / np.sqrt(var_s)
                trend = 'DECREASING'
            else:
                z = 0
                trend = 'NEUTRAL'

            # حساب مستوى الدلالة
            significance = min(abs(z) / 2, 1)  # تطبيع إلى 0-1

            return {
                'trend': trend,
                'significance': significance,
                'z_score': z,
                'statistic': s
            }

        except Exception:
            return {'trend': 'NEUTRAL', 'significance': 0}

    def _check_stationarity(self, data: pd.Series) -> Dict[str, Any]:
        """فحص الاستقرارية (مبسط)"""
        try:
            # حساب المتوسط والتباين للنصف الأول والثاني
            mid_point = len(data) // 2
            first_half = data[:mid_point]
            second_half = data[mid_point:]

            mean_diff = abs(first_half.mean() - second_half.mean())
            var_diff = abs(first_half.var() - second_half.var())

            # معايير بسيطة للاستقرارية
            mean_threshold = data.std() * 0.5
            var_threshold = data.var() * 0.5

            is_stationary = (mean_diff < mean_threshold) and (var_diff < var_threshold)

            return {
                'is_stationary': is_stationary,
                'mean_difference': mean_diff,
                'variance_difference': var_diff
            }

        except Exception:
            return {'is_stationary': True}

    def _determine_signal_from_conditions(self, conditions: List[Dict[str, Any]]) -> str:
        """تحديد الإشارة من الظروف التاريخية الناجحة"""
        try:
            if not conditions:
                return "NEUTRAL"

            # تحليل الظروف الأكثر نجاحاً
            call_conditions = [c for c in conditions if c.get('signal') == 'CALL']
            put_conditions = [c for c in conditions if c.get('signal') == 'PUT']

            if len(call_conditions) > len(put_conditions):
                return "CALL"
            elif len(put_conditions) > len(call_conditions):
                return "PUT"
            else:
                return "NEUTRAL"

        except Exception:
            return "NEUTRAL"

    def _get_analysis_weight(self, analysis: Dict[str, Any]) -> float:
        """تحديد وزن التحليل بناءً على نوعه وجودته"""
        try:
            # أوزان مختلفة لأنواع التحليل المختلفة
            analysis_type = analysis.get('type', 'unknown')
            confidence = analysis.get('confidence', 0)

            base_weights = {
                'z_score': 1.2,      # Z-Score موثوق إحصائياً
                'probability': 1.5,   # التحليل الاحتمالي مهم
                'volatility': 1.0,    # تحليل التقلبات مفيد
                'correlation': 0.8,   # الارتباط مساعد
                'statistical': 1.1,   # الاختبارات الإحصائية موثوقة
                'performance': 1.3    # الأداء التاريخي مهم جداً
            }

            base_weight = base_weights.get(analysis_type, 1.0)

            # تعديل الوزن بناءً على الثقة
            confidence_factor = confidence / 100

            return base_weight * confidence_factor

        except Exception:
            return 1.0

    def _create_error_result(self, error_msg: str) -> Dict[str, Any]:
        """إنشاء نتيجة خطأ"""
        return {
            "layer": self.name,
            "signal": "NEUTRAL",
            "confidence": 0,
            "error": error_msg,
            "timestamp": datetime.now().isoformat()
        }

    def _create_neutral_result(self, reason: str) -> Dict[str, Any]:
        """إنشاء نتيجة محايدة"""
        return {
            "layer": self.name,
            "signal": "NEUTRAL",
            "confidence": 0,
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        }
